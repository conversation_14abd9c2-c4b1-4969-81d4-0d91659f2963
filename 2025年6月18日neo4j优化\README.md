# Neo4j血缘关系查询项目

这是一个基于Spring Boot和Neo4j的血缘关系查询系统，用于查询数据库表和字段之间的血缘关系。

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/
│   │       └── example/
│   │           ├── common/              # 通用类
│   │           │   └── CommonResponse.java
│   │           ├── config/              # 配置类
│   │           │   └── Neo4jConfig.java
│   │           ├── controller/          # 控制器层
│   │           │   └── BloodRelationController.java
│   │           ├── dto/                 # 数据传输对象
│   │           │   ├── TableDTO.java
│   │           │   ├── ColumnDTO.java
│   │           │   └── RelationDTO.java
│   │           ├── repository/          # 数据访问层
│   │           │   └── BloodRelationRepository.java
│   │           ├── service/            # 业务逻辑层
│   │           │   └── BloodRelationService.java
│   │           └── Neo4jBloodRelationApplication.java  # 主启动类
│   └── resources/
│       └── application.yml             # 配置文件
├── pom.xml                            # Maven配置文件
└── README.md                          # 项目说明文档
```

## 主要功能

- **血缘关系查询**: 根据字段ID查询其血缘关系
- **多层级查询**: 支持指定查询深度
- **双向查询**: 支持向上和向下的血缘关系查询
- **数据聚合**: 将查询结果按表和字段进行聚合展示

## 技术栈

- **Java 8**: 开发语言
- **Spring Boot 2.7.18**: Web框架
- **Neo4j**: 图数据库
- **Maven**: 项目管理工具

## 核心类说明

### BloodRelationService
业务逻辑层，负责处理血缘关系查询的核心逻辑：
- `getColumnRelation()`: 主要查询方法，处理字段血缘关系查询

### BloodRelationRepository
数据访问层，封装Neo4j查询操作：
- `getColumnRel()`: 查询字段关系的Neo4j查询
- `getColumnInfo()`: 查询字段基本信息

### DTO类
- `TableDTO`: 表信息数据传输对象
- `ColumnDTO`: 字段信息数据传输对象
- `RelationDTO`: 关系信息数据传输对象

## API接口

### 查询字段血缘关系
```
GET /api/blood-relation/column-relation
```

**参数：**
- `labelName`: 节点标签名称
- `relName`: 关系名称
- `columnId`: 字段ID
- `level`: 查询层级深度
- `flag`: 查询方向（1：向上，其他：向下）

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalLevel": 2,
    "tableList": [
      {
        "columnId": 123,
        "columnName": "字段名",
        "tableName": "表名",
        "dbName": "数据库名",
        "level": 1,
        "columns": [...]
      }
    ]
  }
}
```

## 运行说明

1. 确保Neo4j数据库已启动并运行在默认端口7687
2. 修改`application.yml`中的Neo4j连接配置
3. 运行主启动类`Neo4jBloodRelationApplication`
4. 访问`http://localhost:8080/api/blood-relation/column-relation`进行测试

## 注意事项

- 项目兼容Java 8
- 需要确保Neo4j数据库中已存在相应的数据模型
- 使用了APOC插件的路径扩展功能，需要确保Neo4j中已安装APOC插件