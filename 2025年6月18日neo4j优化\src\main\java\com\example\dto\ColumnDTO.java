package com.example.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(value = "字段信息", description = "数据表中的字段信息及其血缘关系")
public class ColumnDTO {
    
    @ApiModelProperty(value = "字段唯一标识ID", example = "1001")
    private Integer columnId;
    
    @ApiModelProperty(value = "字段名称", example = "user_id")
    private String columnName;
    
    @ApiModelProperty(value = "该字段的血缘关系列表，包含所有下游或上游的关联关系")
    private List<RelationDTO> relations;

    public Integer getColumnId() {
        return columnId;
    }

    public void setColumnId(Integer columnId) {
        this.columnId = columnId;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public List<RelationDTO> getRelations() {
        return relations;
    }

    public void setRelations(List<RelationDTO> relations) {
        this.relations = relations;
    }
} 