package com.example.repository;

import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public class BloodRelationRepository {

    @Autowired
    private Session session;

    public List<Map<String, Object>> getColumnRel(String labelName, String relName, Integer columnId, Integer level, String flag) {
        String direction;
        if ("1".equals(flag)) {
            direction = "<";
        } else {
            direction = ">";
        }
        String querySql = "match (c:" + labelName + ") where id(c) = " + columnId +
                " call apoc.path.expand(c,'" + relName + "'" + direction + "'" + ",1," + level + ") YIELD path" +
                " with nodes(path) as node, relationships(path) as rel UNWIND range(0,size(rel)-1) as index" +
                " with node[index] as startNode, rel[index] as rel, node[index+1] as endNode, index" +
                " return distinct id(startNode) as startColumnId," +
                " startNode.colName as startColumnName," +
                " startNode.tblName as startColumnTableName," +
                " startNode.dbName as startColumnDbName," +
                " index as startColumnLevel," +
                " rel.etlJob as relJobName," +
                " rel.sqlNo as sqlNo," +
                " id(rel) as relId," +
                " id(endNode) as endColumnId," +
                " endNode.colName as endColumnName," +
                " endNode.tblName as endColumnTableName," +
                " endNode.dbName as endColumnDbName," +
                " index+1 as endColumnLevel";
        Result result = session.run(querySql);
        return result.list(record -> record.asMap());
    }

    public List<Map<String, Object>> getColumnInfo(String labelName, Integer columnId) {
        String querySql = "match (c:" + labelName + ") where id(c) = " + columnId +
                " return id(c) as startColumnId," +
                " c.colName as startColumnName," +
                " c.tblName as startColumnTableName," +
                " c.dbName as startColumnDbName";
        Result result = session.run(querySql);
        return result.list(record -> record.asMap());
    }

    public List<Map<String, Object>> getAllColumnsByLabel(String labelName) {
        String querySql = "match (c:" + labelName + ") " +
                " return id(c) as columnId," +
                " c.colName as columnName," +
                " c.tblName as tableName," +
                " c.dbName as dbName";
        Result result = session.run(querySql);
        return result.list(record -> record.asMap());
    }
} 