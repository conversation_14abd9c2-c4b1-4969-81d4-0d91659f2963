package com.example.service;

import com.example.repository.BloodRelationRepository;
import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import com.example.common.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BloodRelationService {

    @Autowired
    private BloodRelationRepository bloodRelationRepository;
    
    @Autowired
    private BloodRelationGraphFormatter graphFormatter;

    public CommonResponse getColumnRelation(String labelName, String relName, String dbName, String tableName, Integer columnId, List<Integer> columnIds, Integer level, String flag, Boolean skipTempTable) {
        if (Objects.nonNull(columnId)) {
            // 原有逻辑：查询单个字段的血缘关系
            // 先去取到所有的关系
            List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, columnId, level, flag);
            
            // 如果需要跳过临时表，先处理关系数据
            if (skipTempTable != null && skipTempTable && !CollectionUtils.isEmpty(columnRels)) {
                columnRels = skipTempTablesInRelations(columnRels);
            }
            
            // 如果查不到，先回归表级别
            // 这里query语句需要重新写，根据columnId查当前字段的表信息
            // 然后根据relName查找表级别关系
            List<TableDTO> tableDTOS = new ArrayList<>();
            Map<String, Object> resultMap = new HashMap<>();
            if (CollectionUtils.isEmpty(columnRels)) {
                List<Map<String, Object>> columnInfo = bloodRelationRepository.getColumnInfo(labelName, columnId);
                if (!CollectionUtils.isEmpty(columnInfo)) {
                    Map<String, Object> objectMap = columnInfo.get(0);
                    Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                    Integer startColumnId = startColumnIdLong.intValue();
                    String startColumnName = (String) objectMap.get("startColumnName");
                    String startColumnTableName = (String) objectMap.get("startColumnTableName");
                    String startColumnDbName = (String) objectMap.get("startColumnDbName");
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setTableName(startColumnTableName);
                    tableDTO.setDbName(startColumnDbName);
                    List<ColumnDTO> columnDTOS = new ArrayList<>();
                    ColumnDTO columnDTO = new ColumnDTO();
                    columnDTO.setColumnId(startColumnId);
                    columnDTO.setColumnName(startColumnName);
                    columnDTOS.add(columnDTO);
                    tableDTO.setColumns(columnDTOS);
                    tableDTO.setLevel(0);
                    tableDTOS.add(tableDTO);
                    resultMap.put("totalLevel", 1);
                }
            } else {
                // 注意：这里的Map类型在图片中存在笔误，但其使用方式是 `Map<String, Map<String, List<RelationDTO>>>`
                Map<String, Map<String, List<RelationDTO>>> dbTableMap = new HashMap<>();
                Map<String, Integer> dbTableLevelMap = new HashMap<>();
                for (int i = 0; i < columnRels.size(); i++) {
                    Map<String, Object> objectMap = columnRels.get(i);
                    // 库名——表名拼接作为map里的key方便遍历
                    String startColumnDbName = (String) objectMap.get("startColumnDbName");
                    String startColumnTableName = (String) objectMap.get("startColumnTableName");
                    String startColumnName = (String) objectMap.get("startColumnName");
                    Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                    Integer startColumnId = startColumnIdLong.intValue();

                    String endColumnDbName = (String) objectMap.get("endColumnDbName");
                    String endColumnTableName = (String) objectMap.get("endColumnTableName");
                    String dbTableName = endColumnDbName + "|" + endColumnTableName;

                    String endColumnName = (String) objectMap.get("endColumnName");
                    Long endColumnIdLong = (Long) objectMap.get("endColumnId");
                    Integer endColumnId = endColumnIdLong.intValue();

                    String relJobName = (String) objectMap.get("relJobName");
                    String sqlNo = (String) objectMap.get("sqlNo");
                    String eltSystem = (String) objectMap.get("eltSystem");

                    RelationDTO relationDTO = new RelationDTO();
                    relationDTO.setColumnId(startColumnId);
                    relationDTO.setColumnName(startColumnName);
                    relationDTO.setRelJobName(relJobName);
                    relationDTO.setSqlNo(sqlNo);
                    relationDTO.setEltSystem(eltSystem);
                    
                    // 设置跳过的临时表信息（如果有的话）
                    @SuppressWarnings("unchecked")
                    List<String> skippedTempTables = (List<String>) objectMap.get("skippedTempTableList");
                    if (skippedTempTables != null && !skippedTempTables.isEmpty()) {
                        relationDTO.setSkippedTempTables(skippedTempTables);
                    }

                    String idName = endColumnId + "|" + endColumnName;
                    // 判断dbTableMap中是否已存在该表信息，若已存在，则进行合并
                    if (dbTableMap.containsKey(dbTableName)) {
                        Map<String, List<RelationDTO>> columnMap = dbTableMap.get(dbTableName);
                        // 判断columnMap中是否已存在该字段，若已存在，则将对应关系添加进去
                        if (columnMap.containsKey(idName)) {
                            List<RelationDTO> relationDTOS = columnMap.get(idName);
                            relationDTOS.add(relationDTO);
                        } else {
                            List<RelationDTO> relationDTOS = new ArrayList<>();
                            relationDTOS.add(relationDTO);
                            columnMap.put(idName, relationDTOS);
                        }
                    } else {
                        Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                        List<RelationDTO> relationDTOS = new ArrayList<>();
                        relationDTOS.add(relationDTO);
                        columnMap.put(idName, relationDTOS);
                        dbTableMap.put(dbTableName, columnMap);
                    }

                    // 获取下游节点的层级
                    Long endColumnLevelLong = (Long) objectMap.get("endColumnLevel");
                    Integer endColumnLevel = endColumnLevelLong.intValue();
                    // 获取并存放每个表的最小层级
                    if (dbTableLevelMap.containsKey(dbTableName)) {
                        Integer value = dbTableLevelMap.get(dbTableName);
                        if (endColumnLevel < value) {
                            dbTableLevelMap.put(dbTableName, endColumnLevel);
                        }
                    } else {
                        dbTableLevelMap.put(dbTableName, endColumnLevel);
                    }

                    // 获取并存放上游节点的层级
                    dbTableName = startColumnDbName + "|" + startColumnTableName;
                    if (!dbTableMap.containsKey(dbTableName)) {
                        Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                        List<RelationDTO> relationDTOS = new ArrayList<>();
                        // 注意：这里使用了下游节点的idName，与图片行为保持一致
                        idName = startColumnId + "|" + startColumnName;
                        columnMap.put(idName, relationDTOS);
                        dbTableMap.put(dbTableName, columnMap);
                    }
                    Long startColumnLevelLong = (Long) objectMap.get("startColumnLevel");
                    Integer startColumnLevel = startColumnLevelLong.intValue();
                    if (dbTableLevelMap.containsKey(dbTableName)) {
                        Integer value = dbTableLevelMap.get(dbTableName);
                        if (startColumnLevel < value) {
                            dbTableLevelMap.put(dbTableName, startColumnLevel);
                        }
                    } else {
                        dbTableLevelMap.put(dbTableName, startColumnLevel);
                    }
                }

                List<Integer> dbTableLevelList = dbTableLevelMap.values().stream().distinct().sorted().collect(Collectors.toList());
                Map<String, Integer> dbTableLevelMapNew = new HashMap<>();
                dbTableLevelMap.forEach((k, v) -> {
                    v = dbTableLevelList.indexOf(v);
                    dbTableLevelMapNew.put(k, v);
                });

                System.out.println(dbTableLevelList);
                System.out.println(dbTableLevelMapNew);

                for (Map.Entry<String, Map<String, List<RelationDTO>>> dbTableEntry : dbTableMap.entrySet()) {
                    String dbTableName = dbTableEntry.getKey();
                    String[] split = dbTableName.split("\\|");
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setDbName(split[0]);
                    tableDTO.setTableName(split[1]);
                    List<ColumnDTO> columnDTOS = new ArrayList<>();
                    Map<String, List<RelationDTO>> columnMap = dbTableEntry.getValue();
                    for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
                        String idName = columnEntry.getKey();
                        String[] split1 = idName.split("\\|");
                        List<RelationDTO> relationDTOS = columnEntry.getValue();
                        ColumnDTO columnDTO = new ColumnDTO();
                        columnDTO.setColumnId(Integer.parseInt(split1[0]));
                        columnDTO.setColumnName(split1[1]);
                        columnDTO.setRelations(relationDTOS);
                        columnDTOS.add(columnDTO);
                    }
                    tableDTO.setColumns(columnDTOS);
                    Integer value = dbTableLevelMapNew.get(dbTableName);
                    tableDTO.setLevel(value);
                    tableDTOS.add(tableDTO);
                }

                Integer totalLevel = 0;
                for (Map.Entry<String, Integer> dbTableLevelEntry : dbTableLevelMapNew.entrySet()) {
                    Integer value = dbTableLevelEntry.getValue() + 1;
                    if (value > totalLevel) {
                        totalLevel = value;
                    }
                }
                resultMap.put("totalLevel", totalLevel);
            }
            resultMap.put("tableList", tableDTOS);
            
            // 打印血缘关系图到日志（使用大一统血缘树显示策略）
            graphFormatter.printBloodRelationGraph(tableDTOS, skipTempTable != null ? skipTempTable : false, BloodRelationGraphFormatter.DisplayStrategy.UNIFIED_TREE, flag);
            
            return new CommonResponse<>(resultMap);
        } else if (Objects.nonNull(columnIds) && !columnIds.isEmpty()) {
            // 新逻辑：查询多个指定字段的血缘关系
            return processMultipleColumnsBloodRelation(labelName, relName, columnIds, level, flag, skipTempTable != null ? skipTempTable : false);
        } else {
            // 原逻辑：查询整个表的所有字段的血缘关系
            return processAllColumnsBloodRelation(labelName, relName, dbName, tableName, level, flag, skipTempTable != null ? skipTempTable : false);
        }
    }
    
    /**
     * 处理整个表所有字段的血缘关系查询
     */
    private CommonResponse processAllColumnsBloodRelation(String labelName, String relName, String dbName, String tableName, Integer level, String flag, boolean skipTempTable) {
        List<TableDTO> tableDTOS = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        
        // 1. 获取该标签下参与了指定关系的所有字段
        List<Map<String, Object>> allColumns = bloodRelationRepository.getAllColumnsWithRelation(labelName, relName, dbName, tableName);
        
        if (CollectionUtils.isEmpty(allColumns)) {
            // 如果该表没有参与指定关系的字段，返回空结果
            resultMap.put("totalLevel", 0);
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        }
        
        System.out.println("Found " + allColumns.size() + " columns with relation " + relName);
        
        // 2. 初始化全局聚合容器
        Map<String, Map<String, List<RelationDTO>>> globalDbTableMap = new HashMap<>();
        Map<String, Integer> globalDbTableLevelMap = new HashMap<>();
        
        // 3. 批量处理每个字段的血缘关系
        for (Map<String, Object> column : allColumns) {
            processColumnBloodRelation(column, labelName, relName, level, flag, 
                                     globalDbTableMap, globalDbTableLevelMap, skipTempTable);
        }
        
        // 4. 如果没有找到任何血缘关系，返回默认结果
        if (globalDbTableMap.isEmpty()) {
            resultMap.put("totalLevel", 0);
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        }
        
        // 5. 层级归一化处理
        Map<String, Integer> normalizedLevelMap = normalizeLevels(globalDbTableLevelMap);
        
        // 6. 构建返回的TableDTO列表
        buildTableDTOList(globalDbTableMap, normalizedLevelMap, tableDTOS);
        
        // 7. 计算最大层级
        Integer totalLevel = calculateTotalLevel(normalizedLevelMap);
        
        resultMap.put("totalLevel", totalLevel);
        resultMap.put("tableList", tableDTOS);
        
        // 打印血缘关系图到日志（使用大一统血缘树显示策略）
        graphFormatter.printBloodRelationGraph(tableDTOS, skipTempTable, BloodRelationGraphFormatter.DisplayStrategy.UNIFIED_TREE, flag);
        
        return new CommonResponse<>(resultMap);
    }
    
    /**
     * 处理多个指定字段的血缘关系查询
     */
    private CommonResponse processMultipleColumnsBloodRelation(String labelName, String relName, List<Integer> columnIds, Integer level, String flag, boolean skipTempTable) {
        List<TableDTO> tableDTOS = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        
        logger.info("Processing blood relation for {} specified column IDs: {}", columnIds.size(), columnIds);
        
        // 1. 直接使用提供的columnIds作为初始字段集合进行批量查询
        List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRels(labelName, relName, columnIds, level, flag);
        
        if (CollectionUtils.isEmpty(columnRels)) {
            // 如果没有找到任何血缘关系，返回空结果
            logger.info("No blood relations found for the specified column IDs");
            resultMap.put("totalLevel", 0);
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        }
        
        // 2. 如果需要跳过临时表，先处理关系数据
        if (skipTempTable) {
            columnRels = skipTempTablesInRelations(columnRels);
        }
        
        // 3. 初始化全局聚合容器
        Map<String, Map<String, List<RelationDTO>>> globalDbTableMap = new HashMap<>();
        Map<String, Integer> globalDbTableLevelMap = new HashMap<>();
        
        // 4. 处理所有血缘关系数据
        for (Map<String, Object> relationData : columnRels) {
            processRelation(relationData, globalDbTableMap, globalDbTableLevelMap);
        }
        
        // 5. 如果没有找到任何血缘关系，返回默认结果
        if (globalDbTableMap.isEmpty()) {
            resultMap.put("totalLevel", 0);
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        }
        
        // 6. 层级归一化处理
        Map<String, Integer> normalizedLevelMap = normalizeLevels(globalDbTableLevelMap);
        
        // 7. 构建返回的TableDTO列表
        buildTableDTOList(globalDbTableMap, normalizedLevelMap, tableDTOS);
        
        // 8. 计算最大层级
        Integer totalLevel = calculateTotalLevel(normalizedLevelMap);
        
        resultMap.put("totalLevel", totalLevel);
        resultMap.put("tableList", tableDTOS);
        
        // 打印血缘关系图到日志（使用大一统血缘树显示策略）
        graphFormatter.printBloodRelationGraph(tableDTOS, skipTempTable, BloodRelationGraphFormatter.DisplayStrategy.UNIFIED_TREE, flag);
        
        logger.info("Multiple columns blood relation processing completed. Total level: {}, Tables count: {}", totalLevel, tableDTOS.size());
        
        return new CommonResponse<>(resultMap);
    }
    
    /**
     * 处理单个字段的血缘关系，并聚合到全局容器中
     */
    private void processColumnBloodRelation(Map<String, Object> column, 
                                          String labelName, 
                                          String relName, 
                                          Integer level, 
                                          String flag,
                                          Map<String, Map<String, List<RelationDTO>>> globalDbTableMap,
                                          Map<String, Integer> globalDbTableLevelMap,
                                          boolean skipTempTable) {
        
        // 提取当前字段信息
        Long currentColumnIdLong = (Long) column.get("columnId");
        Integer currentColumnId = currentColumnIdLong.intValue();
        String currentColumnName = (String) column.get("columnName");
        String currentTableName = (String) column.get("tableName");
        String currentDbName = (String) column.get("dbName");
        
        // 查询当前字段的血缘关系
        List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(
            labelName, relName, currentColumnId, level, flag);
        
        String currentDbTableKey = currentDbName + "|" + currentTableName;
        String currentIdName = currentColumnId + "|" + currentColumnName;
        
        if (CollectionUtils.isEmpty(columnRels)) {
            // 无血缘关系的字段也要加入结果中，确保展示该表的所有字段
            ensureTableAndColumnExist(globalDbTableMap, globalDbTableLevelMap, 
                                    currentDbTableKey, currentIdName, 0);
        } else {
            // 如果需要跳过临时表，先处理关系数据
            if (skipTempTable) {
                columnRels = skipTempTablesInRelations(columnRels);
            }
            
            // 处理有血缘关系的情况
            for (Map<String, Object> relationData : columnRels) {
                processRelation(relationData, globalDbTableMap, globalDbTableLevelMap);
            }
        }
    }
    
    /**
     * 处理单条血缘关系数据
     */
    private void processRelation(Map<String, Object> relationData,
                               Map<String, Map<String, List<RelationDTO>>> globalDbTableMap,
                               Map<String, Integer> globalDbTableLevelMap) {
        
        // 提取关系数据
        RelationInfo startInfo = extractNodeInfo(relationData, "start");
        RelationInfo endInfo = extractNodeInfo(relationData, "end");
        
        String relJobName = (String) relationData.get("relJobName");
        String sqlNo = (String) relationData.get("sqlNo");
        String eltSystem = (String) relationData.get("eltSystem");
        
        // 创建关系DTO（关系的来源是startColumn）
        RelationDTO relationDTO = new RelationDTO();
        relationDTO.setColumnId(startInfo.columnId);
        relationDTO.setColumnName(startInfo.columnName);
        relationDTO.setRelJobName(relJobName);
        relationDTO.setSqlNo(sqlNo);
        relationDTO.setEltSystem(eltSystem);
        
        // 设置跳过的临时表信息（如果有的话）
        @SuppressWarnings("unchecked")
        List<String> skippedTempTables = (List<String>) relationData.get("skippedTempTableList");
        if (skippedTempTables != null && !skippedTempTables.isEmpty()) {
            relationDTO.setSkippedTempTables(skippedTempTables);
        }
        
        // 处理下游节点（endColumn）
        String endDbTableKey = endInfo.dbName + "|" + endInfo.tableName;
        String endIdName = endInfo.columnId + "|" + endInfo.columnName;
        addRelationToMap(globalDbTableMap, endDbTableKey, endIdName, relationDTO);
        updateTableLevel(globalDbTableLevelMap, endDbTableKey, endInfo.level);
        
        // 处理上游节点（startColumn）- 确保起始节点也在结果中
        String startDbTableKey = startInfo.dbName + "|" + startInfo.tableName;
        String startIdName = startInfo.columnId + "|" + startInfo.columnName;
        ensureTableAndColumnExist(globalDbTableMap, globalDbTableLevelMap, 
                                startDbTableKey, startIdName, startInfo.level);
    }
    
    /**
     * 从关系数据中提取节点信息
     */
    private RelationInfo extractNodeInfo(Map<String, Object> relationData, String prefix) {
        RelationInfo info = new RelationInfo();
        info.columnId = ((Long) relationData.get(prefix + "ColumnId")).intValue();
        info.columnName = (String) relationData.get(prefix + "ColumnName");
        info.tableName = (String) relationData.get(prefix + "ColumnTableName");
        info.dbName = (String) relationData.get(prefix + "ColumnDbName");
        info.level = ((Long) relationData.get(prefix + "ColumnLevel")).intValue();
        return info;
    }
    
    /**
     * 确保表和字段在Map中存在
     */
    private void ensureTableAndColumnExist(Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                         Map<String, Integer> dbTableLevelMap,
                                         String dbTableKey,
                                         String idName,
                                         Integer level) {
        // 确保表存在
        dbTableMap.putIfAbsent(dbTableKey, new HashMap<>());
        // 确保字段存在
        dbTableMap.get(dbTableKey).putIfAbsent(idName, new ArrayList<>());
        // 更新层级
        updateTableLevel(dbTableLevelMap, dbTableKey, level);
    }
    
    /**
     * 添加关系到Map中
     */
    private void addRelationToMap(Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                String dbTableKey,
                                String idName,
                                RelationDTO relationDTO) {
        dbTableMap.putIfAbsent(dbTableKey, new HashMap<>());
        dbTableMap.get(dbTableKey).putIfAbsent(idName, new ArrayList<>());
        dbTableMap.get(dbTableKey).get(idName).add(relationDTO);
    }
    
    /**
     * 更新表的最小层级
     */
    private void updateTableLevel(Map<String, Integer> dbTableLevelMap, 
                                String dbTableKey, 
                                Integer level) {
        dbTableLevelMap.merge(dbTableKey, level, Math::min);
    }
    
    /**
     * 层级归一化处理
     */
    private Map<String, Integer> normalizeLevels(Map<String, Integer> dbTableLevelMap) {
        // 获取所有出现的层级并排序
        List<Integer> uniqueLevels = dbTableLevelMap.values().stream()
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        
        // 将原始层级映射到连续的索引
        Map<String, Integer> normalizedMap = new HashMap<>();
        dbTableLevelMap.forEach((tableName, originalLevel) -> {
            int normalizedLevel = uniqueLevels.indexOf(originalLevel);
            normalizedMap.put(tableName, normalizedLevel);
        });
        
        System.out.println("All columns level list: " + uniqueLevels);
        System.out.println("All columns normalized level map: " + normalizedMap);
        
        return normalizedMap;
    }
    
    /**
     * 构建TableDTO列表
     */
    private void buildTableDTOList(Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                 Map<String, Integer> normalizedLevelMap,
                                 List<TableDTO> tableDTOS) {
        
        for (Map.Entry<String, Map<String, List<RelationDTO>>> tableEntry : dbTableMap.entrySet()) {
            String dbTableKey = tableEntry.getKey();
            String[] parts = dbTableKey.split("\\|");
            
            // 创建TableDTO
            TableDTO tableDTO = new TableDTO();
            tableDTO.setDbName(parts[0]);
            tableDTO.setTableName(parts[1]);
            tableDTO.setLevel(normalizedLevelMap.getOrDefault(dbTableKey, 0));
            
            // 构建该表的所有字段
            List<ColumnDTO> columns = buildColumnDTOList(tableEntry.getValue());
            tableDTO.setColumns(columns);
            
            tableDTOS.add(tableDTO);
        }
    }
    
    /**
     * 构建ColumnDTO列表
     */
    private List<ColumnDTO> buildColumnDTOList(Map<String, List<RelationDTO>> columnMap) {
        List<ColumnDTO> columns = new ArrayList<>();
        
        for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
            String idName = columnEntry.getKey();
            String[] parts = idName.split("\\|");
            
            ColumnDTO columnDTO = new ColumnDTO();
            columnDTO.setColumnId(Integer.parseInt(parts[0]));
            columnDTO.setColumnName(parts[1]);
            columnDTO.setRelations(columnEntry.getValue());
            
            columns.add(columnDTO);
        }
        
        return columns;
    }
    
    /**
     * 计算最大层级
     */
    private Integer calculateTotalLevel(Map<String, Integer> normalizedLevelMap) {
        return normalizedLevelMap.values().stream()
                .mapToInt(level -> level + 1)
                .max()
                .orElse(0);
    }
    
    /**
     * 核心方法：处理跳过临时表的逻辑
     * 确保准确实现需求：跳过所有临时表节点，包括起点、中间和终点的临时表
     */
    private List<Map<String, Object>> skipTempTablesInRelations(List<Map<String, Object>> relations) {
        // 1. 先构建一个关系图，方便查找
        Map<Integer, List<Map<String, Object>>> nodeRelationsMap = new HashMap<>();
        Set<Integer> tempNodeIds = new HashSet<>();
        
        // 识别所有临时表节点（包括起点和终点）
        for (Map<String, Object> rel : relations) {
            // 检查起点是否是临时表
            Integer startTempFlag = getIntegerValue(rel.get("startColumnTempFlag"));
            if (startTempFlag != null && startTempFlag == 1) {
                Integer startId = ((Long) rel.get("startColumnId")).intValue();
                tempNodeIds.add(startId);
            }
            
            // 检查终点是否是临时表
            Integer endTempFlag = getIntegerValue(rel.get("endColumnTempFlag"));
            if (endTempFlag != null && endTempFlag == 1) {
                Integer endId = ((Long) rel.get("endColumnId")).intValue();
                tempNodeIds.add(endId);
            }
            
            // 构建节点的出边关系
            Integer startId = ((Long) rel.get("startColumnId")).intValue();
            nodeRelationsMap.putIfAbsent(startId, new ArrayList<>());
            nodeRelationsMap.get(startId).add(rel);
        }
        
        // 2. 找出所有非临时表节点作为潜在的起点
        Set<Integer> nonTempNodes = new HashSet<>();
        for (Map<String, Object> rel : relations) {
            Integer startId = ((Long) rel.get("startColumnId")).intValue();
            Integer endId = ((Long) rel.get("endColumnId")).intValue();
            
            if (!tempNodeIds.contains(startId)) {
                nonTempNodes.add(startId);
            }
            if (!tempNodeIds.contains(endId)) {
                nonTempNodes.add(endId);
            }
        }
        
        // 3. 从每个非临时表节点出发，找到它能到达的所有非临时表节点
        List<Map<String, Object>> resultRelations = new ArrayList<>();
        Set<String> processedPairs = new HashSet<>();  // 避免重复
        
        for (Integer sourceNode : nonTempNodes) {
            // 使用BFS查找从当前非临时表节点可达的所有非临时表节点
            List<Map<String, Object>> reachableRelations = findAllNonTempReachableNodes(
                sourceNode, nodeRelationsMap, tempNodeIds, relations);
            
            for (Map<String, Object> rel : reachableRelations) {
                String pairKey = rel.get("startColumnId") + "->" + rel.get("endColumnId");
                if (!processedPairs.contains(pairKey)) {
                    resultRelations.add(rel);
                    processedPairs.add(pairKey);
                }
            }
        }
        
        return resultRelations;
    }
    
    /**
     * 使用BFS找到从源节点可达的所有非临时表节点，并构建相应的关系
     */
    private List<Map<String, Object>> findAllNonTempReachableNodes(
            Integer sourceNode,
            Map<Integer, List<Map<String, Object>>> nodeRelationsMap,
            Set<Integer> tempNodeIds,
            List<Map<String, Object>> allRelations) {
        
        List<Map<String, Object>> results = new ArrayList<>();
        
        // 如果源节点本身就是临时表，则不处理
        if (tempNodeIds.contains(sourceNode)) {
            return results;
        }
        
        // BFS遍历
        Queue<Integer> queue = new LinkedList<>();
        Set<Integer> visited = new HashSet<>();
        Map<Integer, Map<String, Object>> sourceRelationMap = new HashMap<>(); // 记录从源节点到当前节点使用的第一条边
        Map<Integer, List<String>> skippedTablesMap = new HashMap<>(); // 记录跳过的临时表路径
        
        queue.offer(sourceNode);
        visited.add(sourceNode);
        skippedTablesMap.put(sourceNode, new ArrayList<>());
        
        while (!queue.isEmpty()) {
            Integer currentNode = queue.poll();
            List<Map<String, Object>> outRelations = nodeRelationsMap.get(currentNode);
            
            if (outRelations != null) {
                for (Map<String, Object> rel : outRelations) {
                    Integer targetId = ((Long) rel.get("endColumnId")).intValue();
                    
                    if (!visited.contains(targetId)) {
                        visited.add(targetId);
                        
                        // 记录路径信息：如果是从源节点直接出发，使用当前边；否则使用之前记录的边
                        Map<String, Object> sourceRel = currentNode.equals(sourceNode) ? rel : sourceRelationMap.get(currentNode);
                        sourceRelationMap.put(targetId, sourceRel);
                        
                        // 记录跳过的临时表路径
                        List<String> currentSkippedTables = new ArrayList<>(skippedTablesMap.get(currentNode));
                        if (tempNodeIds.contains(currentNode) && !currentNode.equals(sourceNode)) {
                            // 当前节点是临时表，添加到跳过列表
                            String tempTableInfo = getNodeTableInfo(currentNode, allRelations);
                            currentSkippedTables.add(tempTableInfo);
                        }
                        skippedTablesMap.put(targetId, currentSkippedTables);
                        
                        if (!tempNodeIds.contains(targetId)) {
                            // 找到非临时表节点，创建从源节点到该节点的关系
                            Map<String, Object> newRel = createDirectRelation(sourceNode, targetId, sourceRel, allRelations, currentSkippedTables);
                            results.add(newRel);
                        }
                        
                        // 无论是否是临时表，都继续遍历
                        queue.offer(targetId);
                    }
                }
            }
        }
        
        return results;
    }
    
    /**
     * 创建跳过临时表的直接关系
     */
    private Map<String, Object> createDirectRelation(
            Integer sourceNodeId,
            Integer targetNodeId,
            Map<String, Object> firstRelation,
            List<Map<String, Object>> allRelations,
            List<String> skippedTempTables) {
        
        // 找到源节点和目标节点的详细信息
        Map<String, Object> sourceInfo = findNodeInfo(sourceNodeId, allRelations, true);
        Map<String, Object> targetInfo = findNodeInfo(targetNodeId, allRelations, false);
        
        Map<String, Object> newRel = new HashMap<>();
        
        // 设置起点信息
        newRel.put("startColumnId", sourceNodeId.longValue());
        newRel.put("startColumnName", sourceInfo.get("columnName"));
        newRel.put("startColumnTableName", sourceInfo.get("tableName"));
        newRel.put("startColumnDbName", sourceInfo.get("dbName"));
        newRel.put("startColumnTempFlag", sourceInfo.get("tempFlag"));
        newRel.put("startColumnLevel", sourceInfo.get("level"));
        
        // 设置终点信息
        newRel.put("endColumnId", targetNodeId.longValue());
        newRel.put("endColumnName", targetInfo.get("columnName"));
        newRel.put("endColumnTableName", targetInfo.get("tableName"));
        newRel.put("endColumnDbName", targetInfo.get("dbName"));
        newRel.put("endColumnTempFlag", targetInfo.get("tempFlag"));
        newRel.put("endColumnLevel", targetInfo.get("level"));
        
        // 使用第一条边的关系信息
        newRel.put("relJobName", firstRelation.get("relJobName"));
        newRel.put("sqlNo", firstRelation.get("sqlNo"));
        newRel.put("eltSystem", firstRelation.get("eltSystem"));
        newRel.put("relId", firstRelation.get("relId"));
        
        // 标记这是一个跳过了临时表的关系，并记录跳过的临时表
        newRel.put("skippedTempTable", true);
        newRel.put("skippedTempTableList", skippedTempTables);
        
        return newRel;
    }
    
    /**
     * 获取节点的表信息（用于跳过临时表记录）
     */
    private String getNodeTableInfo(Integer nodeId, List<Map<String, Object>> relations) {
        for (Map<String, Object> rel : relations) {
            // 检查起点
            Integer startId = ((Long) rel.get("startColumnId")).intValue();
            if (startId.equals(nodeId)) {
                return rel.get("startColumnDbName") + "." + rel.get("startColumnTableName") + 
                       "." + rel.get("startColumnName") + "(" + nodeId + ")";
            }
            
            // 检查终点
            Integer endId = ((Long) rel.get("endColumnId")).intValue();
            if (endId.equals(nodeId)) {
                return rel.get("endColumnDbName") + "." + rel.get("endColumnTableName") + 
                       "." + rel.get("endColumnName") + "(" + nodeId + ")";
            }
        }
        return "unknown.table.column(" + nodeId + ")";
    }
    
    /**
     * 从关系列表中查找节点信息
     */
    private Map<String, Object> findNodeInfo(Integer nodeId, List<Map<String, Object>> relations, boolean asStart) {
        Map<String, Object> nodeInfo = new HashMap<>();
        
        for (Map<String, Object> rel : relations) {
            Integer currentId;
            if (asStart) {
                currentId = ((Long) rel.get("startColumnId")).intValue();
                if (currentId.equals(nodeId)) {
                    nodeInfo.put("columnName", rel.get("startColumnName"));
                    nodeInfo.put("tableName", rel.get("startColumnTableName"));
                    nodeInfo.put("dbName", rel.get("startColumnDbName"));
                    nodeInfo.put("tempFlag", rel.get("startColumnTempFlag"));
                    nodeInfo.put("level", rel.get("startColumnLevel"));
                    return nodeInfo;
                }
            } else {
                currentId = ((Long) rel.get("endColumnId")).intValue();
                if (currentId.equals(nodeId)) {
                    nodeInfo.put("columnName", rel.get("endColumnName"));
                    nodeInfo.put("tableName", rel.get("endColumnTableName"));
                    nodeInfo.put("dbName", rel.get("endColumnDbName"));
                    nodeInfo.put("tempFlag", rel.get("endColumnTempFlag"));
                    nodeInfo.put("level", rel.get("endColumnLevel"));
                    return nodeInfo;
                }
            }
        }
        
        return nodeInfo;
    }
    
    private Integer getIntegerValue(Object value) {
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Long) return ((Long) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 内部类：用于临时存储关系节点信息
     */
    private static class RelationInfo {
        Integer columnId;
        String columnName;
        String tableName;
        String dbName;
        Integer level;
    }
} 