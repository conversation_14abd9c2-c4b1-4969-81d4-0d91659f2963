package com.example.controller;

import com.example.service.BloodRelationService;
import com.example.common.CommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "血缘关系查询", description = "数据血缘关系查询相关接口")
@RestController
@RequestMapping("/api/blood-relation")
public class BloodRelationController {

    @Autowired
    private BloodRelationService bloodRelationService;

    @ApiOperation(
            value = "查询数据血缘关系",
            notes = "根据指定条件查询数据血缘关系，支持单字段查询、多字段查询和整表查询三种模式。" +
                   "当提供columnId时进行单字段血缘查询；当提供columnIds时进行多字段批量血缘查询；" +
                   "当都不提供时进行整表所有字段的血缘查询。系统会生成大一统血缘关系树，清晰展示数据流向和处理层级。"
    )
    @GetMapping("/column-relation")
    public CommonResponse getColumnRelation(
            @ApiParam(value = "节点标签名称", required = true, example = "COLUMN_EAST")
            @RequestParam String labelName,
            
            @ApiParam(value = "关系类型名称", required = true, example = "COLUMN_EAST_REL")
            @RequestParam String relName,
            
            @ApiParam(value = "数据库名称", required = true, example = "east")
            @RequestParam String dbName,
            
            @ApiParam(value = "表名称", required = true, example = "user_info")
            @RequestParam String tableName,
            
            @ApiParam(value = "字段ID（可选）。如果提供，则查询单个字段的血缘关系；如果不提供，则查询整个表所有字段的血缘关系", 
                      required = false, example = "1001")
            @RequestParam(required = false) Integer columnId,
            
            @ApiParam(value = "查询层级深度。指定血缘关系的最大层级数，用于控制查询的深度范围", 
                      required = true, example = "3")
            @RequestParam Integer level,
            
            @ApiParam(value = "查询方向标识。1表示向上查询（查找数据来源），0表示向下查询（查找数据去向）", 
                      required = true, allowableValues = "0,1", example = "0")
            @RequestParam String flag,
            
            @ApiParam(value = "是否跳过临时表。true表示在血缘关系中跳过临时表节点，直接连接最终目标表；false表示显示完整路径", 
                      required = false, example = "false")
            @RequestParam(required = false, defaultValue = "false") Boolean skipTempTable) {
        return bloodRelationService.getColumnRelation(labelName, relName, dbName, tableName, columnId, level, flag, skipTempTable);
    }
}