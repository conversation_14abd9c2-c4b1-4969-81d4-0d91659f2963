# 数据血缘关系查询服务说明

## 一、概述

本服务基于Neo4j图数据库，提供数据表和字段之间的血缘关系查询功能。血缘关系记录了数据在ETL处理过程中的流转路径，帮助用户进行数据溯源、影响分析和依赖管理。

## 二、核心概念

### 2.1 什么是数据血缘关系？

数据血缘关系是数据之间的"族谱"关系，描述了数据字段之间的派生、转换和传递关系：

- **上游（数据来源）**：数据从哪里来，相当于"父母"
- **下游（数据去向）**：数据到哪里去，相当于"子女"

### 2.2 血缘关系的产生

血缘关系通过ETL作业（SQL脚本）建立：

```sql
-- 示例：BD_ODS_LCCPXSMX704.sql
INSERT INTO bd_ods_lccpxsmx (cino, khtybh, worktime)
SELECT 
    customer_no as cino,      -- 建立血缘关系：customer_no → cino
    customer_type as khtybh,  -- 建立血缘关系：customer_type → khtybh
    work_time as worktime     -- 建立血缘关系：work_time → worktime
FROM source_table;
```

### 2.3 Neo4j存储模型

在Neo4j图数据库中，血缘关系存储为：

```
节点：字段（标签：COLUMN_EAST）
关系：血缘关系（类型：COLUMN_EAST_REL）

(源字段) -[COLUMN_EAST_REL {relJobName: "xxx.sql", sqlNo: "1"}]-> (目标字段)
```

## 三、接口说明

### 3.1 接口定义

```java
GET /blood-relation-controller/getColumnRelation

参数：
- labelName: String     // Neo4j节点标签，如 "COLUMN_EAST"
- relName: String       // Neo4j关系类型，如 "COLUMN_EAST_REL"  
- dbName: String        // 数据库名，如 "east"
- tableName: String     // 表名，如 "bd_ods_lccpxsmx"
- columnId: Integer     // 字段ID（可选）
- level: Integer        // 查询层级深度
- flag: String          // 查询方向标识
```

### 3.2 查询模式

1. **指定字段查询**（columnId不为空）：查询特定字段的血缘关系
2. **全表查询**（columnId为空）：查询整个表所有字段的血缘关系

## 四、查询逻辑详解

### 4.1 为什么查询一个表会返回多个表？

血缘关系查询的本质是查询**整个数据流转链路**，而不是单个表的信息。

```
示例场景：查询 bd_ods_lccpxsmx 表

表A.字段1 ─────→ bd_ods_lccpxsmx.字段x ─────→ 表B.字段1
                       ↓
                  表C.字段2

查询结果会包含：表A（上游）、bd_ods_lccpxsmx（本身）、表B和表C（下游）
```

### 4.2 查询步骤

#### 步骤1：获取参与血缘关系的字段

```cypher
match (c:COLUMN_EAST)-[:COLUMN_EAST_REL]-() 
where c.dbName = 'east' and c.tblName = 'bd_ods_lccpxsmx' 
return distinct id(c) as columnId, c.colName as columnName
```

**Cypher查询详解：**

- `match (c:COLUMN_EAST)`：匹配标签为`COLUMN_EAST`的节点（代表字段）
- `-[:COLUMN_EAST_REL]-()`：这些节点必须通过`COLUMN_EAST_REL`关系连接到其他节点
  - 使用无向边`-`而非`->`或`<-`，表示不限制关系方向
  - 既包括作为数据源的字段（出边）
  - 也包括作为数据目标的字段（入边）
- `where c.dbName = 'east' and c.tblName = 'bd_ods_lccpxsmx'`：过滤条件
  - **限制对象**：限制的是节点`c`本身的属性（不是关系另一端的节点）
  - **限制时机**：在匹配到所有`COLUMN_EAST`节点后进行过滤
  - **限制效果**：只保留属于`east.bd_ods_lccpxsmx`表的字段节点
  - **重要说明**：这个条件只限制起始查询节点，不限制血缘关系另一端的表。所以查询结果中会出现其他表（因为`bd_ods_lccpxsmx`的字段可能与其他表的字段有血缘关系）
- `return distinct`：返回去重结果（一个字段可能有多个血缘关系）

**查询范围说明：**

此查询会找出`bd_ods_lccpxsmx`表中所有参与了血缘关系的字段，包括：
- ✅ 有数据流入的字段（作为ETL目标）
- ✅ 有数据流出的字段（作为ETL源）
- ❌ 不包括孤立字段（没有任何血缘关系的字段）

#### 步骤2：查询每个字段的完整血缘链路

对每个字段执行深度查询，获取其上下游关系。

#### 步骤3：聚合和层级计算

- 将所有相关表和字段信息聚合
- 计算每个表在血缘链路中的层级
- 层级归一化处理

### 4.3 层级归一化详解

#### 什么是层级归一化？

层级归一化是将不连续的层级值转换为连续索引值的过程。在Neo4j查询中，返回的层级可能是不连续的（如0、3、7、12），需要转换为连续的（0、1、2、3）以便于理解和展示。

#### 归一化过程示例

**原始层级数据（从Neo4j查询得到）：**
```
表A: level = 0   // 远端上游
表B: level = 3   // 直接上游
表C: level = 5   // 查询的起始表
表D: level = 7   // 直接下游
表E: level = 12  // 远端下游
```

**归一化步骤：**

1. **提取唯一层级值并排序**：`[0, 3, 5, 7, 12]`

2. **建立映射关系**：
   ```
   原始层级 → 归一化层级（索引位置）
   0  → 0
   3  → 1
   5  → 2
   7  → 3
   12 → 4
   ```

3. **归一化结果**：
   ```
   表A: level = 0  // 最上游
   表B: level = 1  // 上游
   表C: level = 2  // 起始表
   表D: level = 3  // 下游
   表E: level = 4  // 最下游
   ```

#### 归一化的意义

- **连续性**：层级变为连续的0、1、2、3、4，便于理解
- **相对关系不变**：表之间的远近关系保持不变
- **展示友好**：前端可以按照连续层级进行分层布局

## 五、返回数据结构

### 5.1 整体结构

```json
{
    "code": "0",
    "msg": "success",
    "result": {
        "totalLevel": 4,        // 血缘链路的最大深度
        "tableList": [...]      // 所有相关表的列表
    }
}
```

### 5.2 详细数据结构

```json
{
    "totalLevel": 4,
    "tableList": [
        {
            "tableId": null,
            "tableName": "bd_ods_lccpxsmx",
            "dbName": "east",
            "level": 0,              // 0表示查询起点
            "columns": [
                {
                    "columnId": 3175103,
                    "columnName": "cino",
                    "relations": [    // 该字段的血缘关系
                        {
                            "columnId": 3175068,
                            "columnName": "khtybh",
                            "relId": null,
                            "relJobName": "BD_ODS_LCCPXSMX704.sql",
                            "sqlNo": "1"
                        }
                    ]
                }
            ]
        },
        {
            "tableName": "source_table",
            "dbName": "east", 
            "level": 1,              // 1表示直接关联
            "columns": [...]
        }
    ]
}
```

### 5.3 字段含义

| 字段 | 含义 |
|------|------|
| totalLevel | 整个血缘链路的最大层级数 |
| level | 表在血缘链路中的层级（0=起点，1=直接关联，2=间接关联...） |
| relations | 字段之间的血缘关系列表 |
| relJobName | 建立血缘关系的ETL作业名 |
| sqlNo | ETL作业中的SQL语句编号 |

## 六、层级说明

```
Level 0: 查询的起始表（如 bd_ods_lccpxsmx）
Level 1: 直接上游或下游表
Level 2: 二级关联表（上游的上游或下游的下游）
Level 3: 三级关联表
...以此类推
```

## 七、应用场景

### 7.1 数据溯源
当发现数据质量问题时，可以向上追溯数据来源，定位问题根源。

### 7.2 影响分析
在修改表结构或字段时，可以快速评估影响范围，找出所有受影响的下游表。

### 7.3 依赖管理
了解表之间的依赖关系，优化ETL作业的调度顺序。

### 7.4 数据地图
构建企业数据资产地图，可视化展示数据流转路径。

## 八、注意事项

1. **查询性能**：深层级查询可能涉及大量节点，建议合理设置level参数
2. **数据完整性**：只有通过ETL作业建立的关系才会被记录
3. **孤立字段**：没有血缘关系的字段不会出现在查询结果中

## 九、示例

### 查询请求
```
GET /blood-relation-controller/getColumnRelation?
    labelName=COLUMN_EAST&
    relName=COLUMN_EAST_REL&
    dbName=east&
    tableName=bd_ods_lccpxsmx&
    level=3&
    flag=1
```

### 返回结果解读
- 返回的tableList包含了所有与`bd_ods_lccpxsmx`表有血缘关系的表
- 每个表的level表示其在血缘链路中的位置
- relations记录了具体的字段映射关系和产生关系的ETL作业

通过这个服务，用户可以完整地了解数据的来龙去脉，为数据治理和管理提供有力支持。 