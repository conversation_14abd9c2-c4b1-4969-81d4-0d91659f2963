package com.example.service;

import com.example.repository.BloodRelationRepository;
import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import com.example.common.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BloodRelationService {

    @Autowired
    private BloodRelationRepository bloodRelationRepository;

    public CommonResponse getColumnRelation(String labelName, String relName, Integer columnId, Integer level, String flag) {
        // 先去取到所有的关系
        List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, columnId, level, flag);
        // 如果查不到，先回归表级别
        // 这里query语句需要重新写，根据columnId查当前字段的表信息
        // 然后根据relName查找表级别关系
        List<TableDTO> tableDTOS = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(columnRels)) {
            List<Map<String, Object>> columnInfo = bloodRelationRepository.getColumnInfo(labelName, columnId);
            Map<String, Object> objectMap = columnInfo.get(0);
            Long startColumnIdLong = (Long) objectMap.get("startColumnId");
            Integer startColumnId = startColumnIdLong.intValue();
            String startColumnName = (String) objectMap.get("startColumnName");
            String startColumnTableName = (String) objectMap.get("startColumnTableName");
            String startColumnDbName = (String) objectMap.get("startColumnDbName");
            TableDTO tableDTO = new TableDTO();
            tableDTO.setTableName(startColumnTableName);
            tableDTO.setDbName(startColumnDbName);
            List<ColumnDTO> columnDTOS = new ArrayList<>();
            ColumnDTO columnDTO = new ColumnDTO();
            columnDTO.setColumnId(startColumnId);
            columnDTO.setColumnName(startColumnName);
            columnDTOS.add(columnDTO);
            tableDTO.setColumns(columnDTOS);
            tableDTO.setLevel(0);
            tableDTOS.add(tableDTO);
            resultMap.put("totalLevel", 1);
        } else {
             // 注意：这里的Map类型在图片中存在笔误，但其使用方式是 `Map<String, Map<String, List<RelationDTO>>>`
            Map<String, Map<String, List<RelationDTO>>> dbTableMap = new HashMap<>();
            Map<String, Integer> dbTableLevelMap = new HashMap<>();
            for (int i = 0; i < columnRels.size(); i++) {
                Map<String, Object> objectMap = columnRels.get(i);
                // 
                String startColumnDbName = (String) objectMap.get("startColumnDbName");
                String startColumnTableName = (String) objectMap.get("startColumnTableName");
                String startColumnName = (String) objectMap.get("startColumnName");
                Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                Integer startColumnId = startColumnIdLong.intValue();

                String endColumnDbName = (String) objectMap.get("endColumnDbName");
                String endColumnTableName = (String) objectMap.get("endColumnTableName");
                String endColumnName = (String) objectMap.get("endColumnName");
                Long endColumnIdLong = (Long) objectMap.get("endColumnId");
                Integer endColumnId = endColumnIdLong.intValue();

                String relJobName = (String) objectMap.get("relJobName");
                String sqlNo = (String) objectMap.get("sqlNo");

                RelationDTO relationDTO = new RelationDTO();
                relationDTO.setColumnId(startColumnId);
                relationDTO.setColumnName(startColumnName);
                relationDTO.setRelJobName(relJobName);
                relationDTO.setSqlNo(sqlNo);

                String dbTableName = endColumnDbName + "|" + endColumnTableName;
                String idName = endColumnId + "|" + endColumnName;
                // 判断dbTableMap中是否已存在该表信息，若已存在，则进行合并
                if (dbTableMap.containsKey(dbTableName)) {
                    Map<String, List<RelationDTO>> columnMap = dbTableMap.get(dbTableName);
                    // 判断columnMap中是否已存在该字段，若已存在，则将对应关系添加进去
                    if (columnMap.containsKey(idName)) {
                        List<RelationDTO> relationDTOS = columnMap.get(idName);
                        relationDTOS.add(relationDTO);
                    } else {
                        List<RelationDTO> relationDTOS = new ArrayList<>();
                        relationDTOS.add(relationDTO);
                        columnMap.put(idName, relationDTOS);
                    }
                } else {
                    Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                    List<RelationDTO> relationDTOS = new ArrayList<>();
                    relationDTOS.add(relationDTO);
                    columnMap.put(idName, relationDTOS);
                    dbTableMap.put(dbTableName, columnMap);
                }

                // 获取下游节点的层级
                Long endColumnLevelLong = (Long) objectMap.get("endColumnLevel");
                Integer endColumnLevel = endColumnLevelLong.intValue();
                // 获取并存放每个表的最小层级
                if (dbTableLevelMap.containsKey(dbTableName)) {
                    Integer value = dbTableLevelMap.get(dbTableName);
                    if (endColumnLevel < value) {
                        dbTableLevelMap.put(dbTableName, endColumnLevel);
                    }
                } else {
                    dbTableLevelMap.put(dbTableName, endColumnLevel);
                }

                // 获取并存放上游节点的层级
                String startDbTableName = startColumnDbName + "|" + startColumnTableName;
                if (!dbTableMap.containsKey(startDbTableName)) {
                    Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                    List<RelationDTO> relationDTOS = new ArrayList<>();
                    // 注意：这里使用了下游节点的idName，与图片行为保持一致
                    columnMap.put(idName, relationDTOS);
                    dbTableMap.put(startDbTableName, columnMap);
                }
                Long startColumnLevelLong = (Long) objectMap.get("startColumnLevel");
                Integer startColumnLevel = startColumnLevelLong.intValue();
                if (dbTableLevelMap.containsKey(startDbTableName)) {
                    Integer value = dbTableLevelMap.get(startDbTableName);
                    if (startColumnLevel < value) {
                        dbTableLevelMap.put(startDbTableName, startColumnLevel);
                    }
                } else {
                    dbTableLevelMap.put(startDbTableName, startColumnLevel);
                }
            }

            List<Integer> dbTableLevelList = dbTableLevelMap.values().stream().distinct().sorted().collect(Collectors.toList());
            Map<String, Integer> dbTableLevelMapNew = new HashMap<>();
            dbTableLevelMap.forEach((k, v) -> {
                v = dbTableLevelList.indexOf(v);
                dbTableLevelMapNew.put(k, v);
            });

            System.out.println(dbTableLevelList);
            System.out.println(dbTableLevelMapNew);

            for (Map.Entry<String, Map<String, List<RelationDTO>>> dbTableEntry : dbTableMap.entrySet()) {
                String dbTableName = dbTableEntry.getKey();
                String[] split = dbTableName.split("\\|");
                TableDTO tableDTO = new TableDTO();
                tableDTO.setDbName(split[0]);
                tableDTO.setTableName(split[1]);
                List<ColumnDTO> columnDTOS = new ArrayList<>();
                Map<String, List<RelationDTO>> columnMap = dbTableEntry.getValue();
                for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
                    String idName = columnEntry.getKey();
                    String[] split1 = idName.split("\\|");
                    List<RelationDTO> relationDTOS = columnEntry.getValue();
                    ColumnDTO columnDTO = new ColumnDTO();
                    columnDTO.setColumnId(Integer.parseInt(split1[0]));
                    columnDTO.setColumnName(split1[1]);
                    columnDTO.setRelations(relationDTOS);
                    columnDTOS.add(columnDTO);
                }
                tableDTO.setColumns(columnDTOS);
                Integer value = dbTableLevelMapNew.get(dbTableName);
                tableDTO.setLevel(value);
                tableDTOS.add(tableDTO);
            }

            Integer totalLevel = 0;
            for (Map.Entry<String, Integer> dbTableLevelEntry : dbTableLevelMapNew.entrySet()) {
                Integer value = dbTableLevelEntry.getValue() + 1;
                if (value > totalLevel) {
                    totalLevel = value;
                }
            }
            resultMap.put("totalLevel", totalLevel);
        }

       
        resultMap.put("tableList", tableDTOS);
        return new CommonResponse<>(resultMap);
    }
} 