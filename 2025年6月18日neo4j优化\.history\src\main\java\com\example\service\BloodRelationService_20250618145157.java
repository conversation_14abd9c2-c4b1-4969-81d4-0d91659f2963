package com.example.service;

import com.example.repository.BloodRelationRepository;
import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import com.example.common.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BloodRelationService {

    @Autowired
    private BloodRelationRepository bloodRelationRepository;

    public CommonResponse getColumnRelation(String labelName, String relName, Integer columnId, Integer level, String flag) {
        if (Objects.nonNull(columnId)) {
            // 原有逻辑：查询单个字段的血缘关系
            // 先去取到所有的关系
            List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, columnId, level, flag);
            // 如果查不到，先回归表级别
            // 这里query语句需要重新写，根据columnId查当前字段的表信息
            // 然后根据relName查找表级别关系
            List<TableDTO> tableDTOS = new ArrayList<>();
            Map<String, Object> resultMap = new HashMap<>();
            if (CollectionUtils.isEmpty(columnRels)) {
                List<Map<String, Object>> columnInfo = bloodRelationRepository.getColumnInfo(labelName, columnId);
                Map<String, Object> objectMap = columnInfo.get(0);
                Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                Integer startColumnId = startColumnIdLong.intValue();
                String startColumnName = (String) objectMap.get("startColumnName");
                String startColumnTableName = (String) objectMap.get("startColumnTableName");
                String startColumnDbName = (String) objectMap.get("startColumnDbName");
                TableDTO tableDTO = new TableDTO();
                tableDTO.setTableName(startColumnTableName);
                tableDTO.setDbName(startColumnDbName);
                List<ColumnDTO> columnDTOS = new ArrayList<>();
                ColumnDTO columnDTO = new ColumnDTO();
                columnDTO.setColumnId(startColumnId);
                columnDTO.setColumnName(startColumnName);
                columnDTOS.add(columnDTO);
                tableDTO.setColumns(columnDTOS);
                tableDTO.setLevel(0);
                tableDTOS.add(tableDTO);
                resultMap.put("totalLevel", 1);
            } else {
                // 注意：这里的Map类型在图片中存在笔误，但其使用方式是 `Map<String, Map<String, List<RelationDTO>>>`
                Map<String, Map<String, List<RelationDTO>>> dbTableMap = new HashMap<>();
                Map<String, Integer> dbTableLevelMap = new HashMap<>();
                for (int i = 0; i < columnRels.size(); i++) {
                    Map<String, Object> objectMap = columnRels.get(i);
                    // 库名——表名拼接作为map里的key方便遍历
                    String startColumnDbName = (String) objectMap.get("startColumnDbName");
                    String startColumnTableName = (String) objectMap.get("startColumnTableName");
                    String startColumnName = (String) objectMap.get("startColumnName");
                    Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                    Integer startColumnId = startColumnIdLong.intValue();

                    String endColumnDbName = (String) objectMap.get("endColumnDbName");
                    String endColumnTableName = (String) objectMap.get("endColumnTableName");
                    String dbTableName = endColumnDbName + "|" + endColumnTableName;

                    String endColumnName = (String) objectMap.get("endColumnName");
                    Long endColumnIdLong = (Long) objectMap.get("endColumnId");
                    Integer endColumnId = endColumnIdLong.intValue();

                    String relJobName = (String) objectMap.get("relJobName");
                    String sqlNo = (String) objectMap.get("sqlNo");

                    RelationDTO relationDTO = new RelationDTO();
                    relationDTO.setColumnId(startColumnId);
                    relationDTO.setColumnName(startColumnName);
                    relationDTO.setRelJobName(relJobName);
                    relationDTO.setSqlNo(sqlNo);

                    String idName = endColumnId + "|" + endColumnName;
                    // 判断dbTableMap中是否已存在该表信息，若已存在，则进行合并
                    if (dbTableMap.containsKey(dbTableName)) {
                        Map<String, List<RelationDTO>> columnMap = dbTableMap.get(dbTableName);
                        // 判断columnMap中是否已存在该字段，若已存在，则将对应关系添加进去
                        if (columnMap.containsKey(idName)) {
                            List<RelationDTO> relationDTOS = columnMap.get(idName);
                            relationDTOS.add(relationDTO);
                        } else {
                            List<RelationDTO> relationDTOS = new ArrayList<>();
                            relationDTOS.add(relationDTO);
                            columnMap.put(idName, relationDTOS);
                        }
                    } else {
                        Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                        List<RelationDTO> relationDTOS = new ArrayList<>();
                        relationDTOS.add(relationDTO);
                        columnMap.put(idName, relationDTOS);
                        dbTableMap.put(dbTableName, columnMap);
                    }

                    // 获取下游节点的层级
                    Long endColumnLevelLong = (Long) objectMap.get("endColumnLevel");
                    Integer endColumnLevel = endColumnLevelLong.intValue();
                    // 获取并存放每个表的最小层级
                    if (dbTableLevelMap.containsKey(dbTableName)) {
                        Integer value = dbTableLevelMap.get(dbTableName);
                        if (endColumnLevel < value) {
                            dbTableLevelMap.put(dbTableName, endColumnLevel);
                        }
                    } else {
                        dbTableLevelMap.put(dbTableName, endColumnLevel);
                    }

                    // 获取并存放上游节点的层级
                    dbTableName = startColumnDbName + "|" + startColumnTableName;
                    if (!dbTableMap.containsKey(dbTableName)) {
                        Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                        List<RelationDTO> relationDTOS = new ArrayList<>();
                        // 注意：这里使用了下游节点的idName，与图片行为保持一致
                        idName = startColumnId + "|" + startColumnName;
                        columnMap.put(idName, relationDTOS);
                        dbTableMap.put(dbTableName, columnMap);
                    }
                    Long startColumnLevelLong = (Long) objectMap.get("startColumnLevel");
                    Integer startColumnLevel = startColumnLevelLong.intValue();
                    if (dbTableLevelMap.containsKey(dbTableName)) {
                        Integer value = dbTableLevelMap.get(dbTableName);
                        if (startColumnLevel < value) {
                            dbTableLevelMap.put(dbTableName, startColumnLevel);
                        }
                    } else {
                        dbTableLevelMap.put(dbTableName, startColumnLevel);
                    }
                }

                List<Integer> dbTableLevelList = dbTableLevelMap.values().stream().distinct().sorted().collect(Collectors.toList());
                Map<String, Integer> dbTableLevelMapNew = new HashMap<>();
                dbTableLevelMap.forEach((k, v) -> {
                    v = dbTableLevelList.indexOf(v);
                    dbTableLevelMapNew.put(k, v);
                });

                System.out.println(dbTableLevelList);
                System.out.println(dbTableLevelMapNew);

                for (Map.Entry<String, Map<String, List<RelationDTO>>> dbTableEntry : dbTableMap.entrySet()) {
                    String dbTableName = dbTableEntry.getKey();
                    String[] split = dbTableName.split("\\|");
                    TableDTO tableDTO = new TableDTO();
                    tableDTO.setDbName(split[0]);
                    tableDTO.setTableName(split[1]);
                    List<ColumnDTO> columnDTOS = new ArrayList<>();
                    Map<String, List<RelationDTO>> columnMap = dbTableEntry.getValue();
                    for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
                        String idName = columnEntry.getKey();
                        String[] split1 = idName.split("\\|");
                        List<RelationDTO> relationDTOS = columnEntry.getValue();
                        ColumnDTO columnDTO = new ColumnDTO();
                        columnDTO.setColumnId(Integer.parseInt(split1[0]));
                        columnDTO.setColumnName(split1[1]);
                        columnDTO.setRelations(relationDTOS);
                        columnDTOS.add(columnDTO);
                    }
                    tableDTO.setColumns(columnDTOS);
                    Integer value = dbTableLevelMapNew.get(dbTableName);
                    tableDTO.setLevel(value);
                    tableDTOS.add(tableDTO);
                }

                Integer totalLevel = 0;
                for (Map.Entry<String, Integer> dbTableLevelEntry : dbTableLevelMapNew.entrySet()) {
                    Integer value = dbTableLevelEntry.getValue() + 1;
                    if (value > totalLevel) {
                        totalLevel = value;
                    }
                }
                resultMap.put("totalLevel", totalLevel);
            }
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        } else {
            // 新逻辑：查询整个表的所有字段的血缘关系
            List<TableDTO> tableDTOS = new ArrayList<>();
            Map<String, Object> resultMap = new HashMap<>();
            
            // 1. 先获取该标签下的所有字段
            List<Map<String, Object>> allColumns = bloodRelationRepository.getAllColumnsByLabel(labelName);
            
            if (CollectionUtils.isEmpty(allColumns)) {
                // 如果该表没有字段，返回空结果
                resultMap.put("totalLevel", 0);
                resultMap.put("tableList", tableDTOS);
                return new CommonResponse<>(resultMap);
            }
            
            // 2. 全局的聚合容器
            Map<String, Map<String, List<RelationDTO>>> globalDbTableMap = new HashMap<>();
            Map<String, Integer> globalDbTableLevelMap = new HashMap<>();
            
            // 3. 遍历每个字段，查询其血缘关系并聚合
            for (Map<String, Object> column : allColumns) {
                Long currentColumnIdLong = (Long) column.get("columnId");
                Integer currentColumnId = currentColumnIdLong.intValue();
                String currentColumnName = (String) column.get("columnName");
                String currentTableName = (String) column.get("tableName");
                String currentDbName = (String) column.get("dbName");
                
                // 查询当前字段的血缘关系
                List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, currentColumnId, level, flag);
                
                // 如果当前字段没有血缘关系，确保它自己被加入到结果中
                if (CollectionUtils.isEmpty(columnRels)) {
                    String dbTableKey = currentDbName + "|" + currentTableName;
                    if (!globalDbTableMap.containsKey(dbTableKey)) {
                        globalDbTableMap.put(dbTableKey, new HashMap<>());
                        globalDbTableLevelMap.put(dbTableKey, 0);
                    }
                    String idName = currentColumnId + "|" + currentColumnName;
                    if (!globalDbTableMap.get(dbTableKey).containsKey(idName)) {
                        globalDbTableMap.get(dbTableKey).put(idName, new ArrayList<>());
                    }
                } else {
                    // 处理当前字段的血缘关系
                    for (Map<String, Object> objectMap : columnRels) {
                        // 提取数据
                        String startColumnDbName = (String) objectMap.get("startColumnDbName");
                        String startColumnTableName = (String) objectMap.get("startColumnTableName");
                        String startColumnName = (String) objectMap.get("startColumnName");
                        Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                        Integer startColumnId = startColumnIdLong.intValue();

                        String endColumnDbName = (String) objectMap.get("endColumnDbName");
                        String endColumnTableName = (String) objectMap.get("endColumnTableName");
                        String endColumnName = (String) objectMap.get("endColumnName");
                        Long endColumnIdLong = (Long) objectMap.get("endColumnId");
                        Integer endColumnId = endColumnIdLong.intValue();

                        String relJobName = (String) objectMap.get("relJobName");
                        String sqlNo = (String) objectMap.get("sqlNo");

                        // 构建关系DTO
                        RelationDTO relationDTO = new RelationDTO();
                        relationDTO.setColumnId(startColumnId);
                        relationDTO.setColumnName(startColumnName);
                        relationDTO.setRelJobName(relJobName);
                        relationDTO.setSqlNo(sqlNo);

                        // 处理下游节点
                        String dbTableName = endColumnDbName + "|" + endColumnTableName;
                        String idName = endColumnId + "|" + endColumnName;
                        
                        // 合并到全局Map中
                        globalDbTableMap.putIfAbsent(dbTableName, new HashMap<>());
                        globalDbTableMap.get(dbTableName).putIfAbsent(idName, new ArrayList<>());
                        globalDbTableMap.get(dbTableName).get(idName).add(relationDTO);

                        // 更新层级信息
                        Long endColumnLevelLong = (Long) objectMap.get("endColumnLevel");
                        Integer endColumnLevel = endColumnLevelLong.intValue();
                        globalDbTableLevelMap.merge(dbTableName, endColumnLevel, Math::min);

                        // 处理上游节点
                        dbTableName = startColumnDbName + "|" + startColumnTableName;
                        idName = startColumnId + "|" + startColumnName;
                        
                        globalDbTableMap.putIfAbsent(dbTableName, new HashMap<>());
                        globalDbTableMap.get(dbTableName).putIfAbsent(idName, new ArrayList<>());
                        
                        Long startColumnLevelLong = (Long) objectMap.get("startColumnLevel");
                        Integer startColumnLevel = startColumnLevelLong.intValue();
                        globalDbTableLevelMap.merge(dbTableName, startColumnLevel, Math::min);
                    }
                }
            }
            
            // 4. 层级归一化处理
            List<Integer> dbTableLevelList = globalDbTableLevelMap.values().stream()
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            Map<String, Integer> dbTableLevelMapNew = new HashMap<>();
            globalDbTableLevelMap.forEach((k, v) -> {
                dbTableLevelMapNew.put(k, dbTableLevelList.indexOf(v));
            });

            System.out.println("All columns level list: " + dbTableLevelList);
            System.out.println("All columns level map: " + dbTableLevelMapNew);

            // 5. 构建返回结果
            for (Map.Entry<String, Map<String, List<RelationDTO>>> dbTableEntry : globalDbTableMap.entrySet()) {
                String dbTableName = dbTableEntry.getKey();
                String[] split = dbTableName.split("\\|");
                TableDTO tableDTO = new TableDTO();
                tableDTO.setDbName(split[0]);
                tableDTO.setTableName(split[1]);
                
                List<ColumnDTO> columnDTOS = new ArrayList<>();
                Map<String, List<RelationDTO>> columnMap = dbTableEntry.getValue();
                for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
                    String idName = columnEntry.getKey();
                    String[] split1 = idName.split("\\|");
                    List<RelationDTO> relationDTOS = columnEntry.getValue();
                    ColumnDTO columnDTO = new ColumnDTO();
                    columnDTO.setColumnId(Integer.parseInt(split1[0]));
                    columnDTO.setColumnName(split1[1]);
                    columnDTO.setRelations(relationDTOS);
                    columnDTOS.add(columnDTO);
                }
                tableDTO.setColumns(columnDTOS);
                Integer level = dbTableLevelMapNew.get(dbTableName);
                tableDTO.setLevel(level != null ? level : 0);
                tableDTOS.add(tableDTO);
            }

            // 计算最大层级
            Integer totalLevel = 0;
            for (Integer level : dbTableLevelMapNew.values()) {
                if (level + 1 > totalLevel) {
                    totalLevel = level + 1;
                }
            }
            
            resultMap.put("totalLevel", totalLevel);
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        }
    }
} 