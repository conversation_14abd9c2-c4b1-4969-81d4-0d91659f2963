package com.example.repository;

import org.neo4j.driver.Result;
import org.neo4j.driver.Session;
import org.neo4j.driver.Values;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Map;

@Repository
public class BloodRelationRepository {

    private static final Logger logger = LoggerFactory.getLogger(BloodRelationRepository.class);

    @Autowired
    private Session session;

    public List<Map<String, Object>> getColumnRel(String labelName, String relName, Integer columnId, Integer level, String flag) {
        String relationPattern;
        if ("1".equals(flag)) {
            // 向内查询（上游依赖）
            relationPattern = "<" + relName;
        } else {
            // 向外查询（下游影响）
            relationPattern = relName + ">";
        }
        String querySql = "match (c:" + labelName + ") where id(c) = " + columnId +
                " call apoc.path.expand(c,'" + relationPattern + "',null,1," + level + ") YIELD path" +
                " with nodes(path) as node, relationships(path) as rel UNWIND range(0,size(rel)-1) as index" +
                " with node[index] as startNode, rel[index] as rel, node[index+1] as endNode, index" +
                " return distinct id(startNode) as startColumnId," +
                " startNode.colName as startColumnName," +
                " startNode.tblName as startColumnTableName," +
                " startNode.dbName as startColumnDbName," +
                " startNode.tempFlag as startColumnTempFlag," +
                " index as startColumnLevel," +
                " rel.relJob as relJobName," +
                " rel.sqlNo as sqlNo," +
                " rel.eltSystem as eltSystem," +
                " id(rel) as relId," +
                " id(endNode) as endColumnId," +
                " endNode.colName as endColumnName," +
                " endNode.tblName as endColumnTableName," +
                " endNode.dbName as endColumnDbName," +
                " endNode.tempFlag as endColumnTempFlag," +
                " index+1 as endColumnLevel";
        
        logger.info("Executing Neo4j query: {}", querySql);
        long startTime = System.currentTimeMillis();
        
        Result result = session.run(querySql);
        List<Map<String, Object>> resultList = result.list(record -> record.asMap());
        
        long endTime = System.currentTimeMillis();
        logger.info("Query executed in {} ms, returned {} records", (endTime - startTime), resultList.size());
        
        return resultList;
    }

    public List<Map<String, Object>> getColumnRels(String labelName, String relName, List<Integer> columnIds, Integer level, String flag) {
        // 参数验证
        if (columnIds == null || columnIds.isEmpty()) {
            logger.error("columnIds is null or empty");
            throw new IllegalArgumentException("columnIds cannot be null or empty");
        }
        
        String relationPattern;
        if ("1".equals(flag)) {
            // 向内查询（上游依赖）
            relationPattern = "<" + relName;
        } else {
            // 向外查询（下游影响）
            relationPattern = relName + ">";
        }
        
        // 使用参数化查询，避免SQL注入风险
        String querySql = "MATCH (c:" + labelName + ") WHERE id(c) IN $columnIds " +
                "CALL apoc.path.expand(c,'" + relationPattern + "',null,1," + level + ") YIELD path " +
                "WITH nodes(path) as node, relationships(path) as rel UNWIND range(0,size(rel)-1) as index " +
                "WITH node[index] as startNode, rel[index] as rel, node[index+1] as endNode, index " +
                "RETURN distinct id(startNode) as startColumnId," +
                " startNode.colName as startColumnName," +
                " startNode.tblName as startColumnTableName," +
                " startNode.dbName as startColumnDbName," +
                " startNode.tempFlag as startColumnTempFlag," +
                " index as startColumnLevel," +
                " rel.relJob as relJobName," +
                " rel.sqlNo as sqlNo," +
                " rel.eltSystem as eltSystem," +
                " id(rel) as relId," +
                " id(endNode) as endColumnId," +
                " endNode.colName as endColumnName," +
                " endNode.tblName as endColumnTableName," +
                " endNode.dbName as endColumnDbName," +
                " endNode.tempFlag as endColumnTempFlag," +
                " index+1 as endColumnLevel";
        
        logger.info("Executing Neo4j query for multiple columnIds: {}", querySql);
        logger.info("ColumnIds: {}", columnIds);
        long startTime = System.currentTimeMillis();
        
        // 使用参数绑定传递columnIds
        Result result = session.run(querySql, Values.parameters("columnIds", columnIds));
        List<Map<String, Object>> resultList = result.list(record -> record.asMap());
        
        long endTime = System.currentTimeMillis();
        logger.info("Query executed in {} ms, returned {} records for {} columnIds", (endTime - startTime), resultList.size(), columnIds.size());
        
        return resultList;
    }

    public List<Map<String, Object>> getColumnInfo(String labelName, Integer columnId) {
        String querySql = "match (c:" + labelName + ") where id(c) = " + columnId +
                " return id(c) as startColumnId," +
                " c.colName as startColumnName," +
                " c.tblName as startColumnTableName," +
                " c.dbName as startColumnDbName," +
                " c.tempFlag as startColumnTempFlag";
        
        logger.info("Executing Neo4j query: {}", querySql);
        long startTime = System.currentTimeMillis();
        
        Result result = session.run(querySql);
        List<Map<String, Object>> resultList = result.list(record -> record.asMap());
        
        long endTime = System.currentTimeMillis();
        logger.info("Query executed in {} ms, returned {} records", (endTime - startTime), resultList.size());
        
        return resultList;
    }

    public List<Map<String, Object>> getAllColumnsWithRelation(String labelName, String relName) {
        // 参数验证
        if (labelName == null || labelName.trim().isEmpty()) {
            logger.error("labelName is null or empty");
            throw new IllegalArgumentException("labelName cannot be null or empty");
        }
        if (relName == null || relName.trim().isEmpty()) {
            logger.error("relName is null or empty");
            throw new IllegalArgumentException("relName cannot be null or empty");
        }
        
        // 查询所有参与了指定关系的节点（不管是作为起点还是终点）
        String querySql = "MATCH (c:" + labelName + ")-[:" + relName + "]-() " +
                " RETURN distinct id(c) as columnId," +
                " c.colName as columnName," +
                " c.tblName as tableName," +
                " c.dbName as dbName," +
                " c.tempFlag as tempFlag";
        
        logger.info("Executing Neo4j query with params - labelName: {}, relName: {}", labelName, relName);
        logger.info("Generated query: {}", querySql);
        long startTime = System.currentTimeMillis();
        
        try {
            Result result = session.run(querySql);
            List<Map<String, Object>> resultList = result.list(record -> record.asMap());
            
            long endTime = System.currentTimeMillis();
            logger.info("Query executed successfully in {} ms, returned {} records", (endTime - startTime), resultList.size());
            
            return resultList;
        } catch (Exception e) {
            logger.error("Error executing Neo4j query: {}", e.getMessage(), e);
            throw e;
        }
    }

    public List<Map<String, Object>> getColumnsWithRelation(String labelName, String relName, String dbName, String tableName) {
        // 查询指定数据库、表名下，参与了指定关系的节点
        String querySql = "MATCH (c:" + labelName + ")-[:" + relName + "]-() " +
                " WHERE c.dbName = '" + dbName + "' AND c.tblName = '" + tableName + "'" +
                " RETURN distinct id(c) as columnId," +
                " c.colName as columnName," +
                " c.tblName as tableName," +
                " c.dbName as dbName," +
                " c.tempFlag as tempFlag";
        
        logger.info("Executing Neo4j query: {}", querySql);
        long startTime = System.currentTimeMillis();
        
        Result result = session.run(querySql);
        List<Map<String, Object>> resultList = result.list(record -> record.asMap());
        
        long endTime = System.currentTimeMillis();
        logger.info("Query executed in {} ms, returned {} records", (endTime - startTime), resultList.size());
        
        return resultList;
    }

    public List<Map<String, Object>> getTableRel(String labelName, String relName, String dbName, String tableName, Integer level, String flag, boolean skipTempTable) {
        // 参数验证
        if (labelName == null || labelName.trim().isEmpty()) {
            logger.error("labelName is null or empty");
            throw new IllegalArgumentException("labelName cannot be null or empty");
        }
        if (relName == null || relName.trim().isEmpty()) {
            logger.error("relName is null or empty");
            throw new IllegalArgumentException("relName cannot be null or empty");
        }
        
        String relationPattern;
        if ("1".equals(flag)) {
            // 向内查询（上游依赖）
            relationPattern = "<" + relName;
        } else {
            // 向外查询（下游影响）
            relationPattern = relName + ">";
        }
        
        // 构建标签过滤器（用于跳过临时表）
        String labelFilter = "";
        if (skipTempTable) {
            // 假设临时表通过tempFlag=1标识，我们需要在路径扩展时过滤掉这些节点
            labelFilter = "+tempFlag:0"; // 只允许非临时表节点
        }
        
        // 使用参数化查询构建表级血缘查询
        String querySql;
        if (skipTempTable) {
            // 使用 expandConfig 来支持标签过滤
            querySql = "MATCH (start_node:" + labelName + " {dbName: $dbName, tblName: $tableName}) " +
                    "WITH COLLECT(start_node) AS start_nodes " +
                    "UNWIND start_nodes AS sn " +
                    "CALL apoc.path.expandConfig(sn, {" +
                    "  relationshipFilter: '" + relationPattern + "'," +
                    "  labelFilter: '" + labelFilter + "'," +
                    "  minLevel: 1, maxLevel: " + level + "," +
                    "  uniqueness: 'NODE_GLOBAL'" +
                    "}) YIELD path " +
                    "WITH last(nodes(path)) AS end_node, relationships(path) as rels, length(path) as pathLength " +
                    "WHERE end_node.dbName <> $dbName OR end_node.tblName <> $tableName " +
                    "WITH end_node.dbName as endDbName, end_node.tblName as endTableName, pathLength, " +
                    "     [r in rels | r.relJobName] as jobNames, " +
                    "     [r in rels | r.sqlNo] as sqlNos, " +
                    "     [r in rels | r.eltSystem] as eltSystems " +
                    "RETURN DISTINCT endDbName AS dbName, endTableName AS tableName, " +
                    "       min(pathLength) AS level, " +
                    "       collect(DISTINCT jobNames) AS relJobNames, " +
                    "       collect(DISTINCT sqlNos) AS sqlNos, " +
                    "       collect(DISTINCT eltSystems) AS eltSystems " +
                    "ORDER BY level";
        } else {
            // 不跳过临时表的简化查询
            querySql = "MATCH (start_node:" + labelName + " {dbName: $dbName, tblName: $tableName}) " +
                    "CALL apoc.path.expand(start_node, '" + relationPattern + "', null, 1, " + level + ") YIELD path " +
                    "WITH last(nodes(path)) AS end_node, relationships(path) as rels, length(path) as pathLength " +
                    "WHERE end_node.dbName <> $dbName OR end_node.tblName <> $tableName " +
                    "WITH end_node.dbName as endDbName, end_node.tblName as endTableName, pathLength, " +
                    "     [r in rels | r.relJobName] as jobNames, " +
                    "     [r in rels | r.sqlNo] as sqlNos, " +
                    "     [r in rels | r.eltSystem] as eltSystems " +
                    "RETURN DISTINCT endDbName AS dbName, endTableName AS tableName, " +
                    "       min(pathLength) AS level, " +
                    "       collect(DISTINCT jobNames) AS relJobNames, " +
                    "       collect(DISTINCT sqlNos) AS sqlNos, " +
                    "       collect(DISTINCT eltSystems) AS eltSystems " +
                    "ORDER BY level";
        }
        
        logger.info("Executing table-level Neo4j query: {}", querySql);
        logger.info("Query parameters - dbName: {}, tableName: {}, level: {}, flag: {}, skipTempTable: {}", 
                   dbName, tableName, level, flag, skipTempTable);
        long startTime = System.currentTimeMillis();
        
        try {
            Result result = session.run(querySql, Values.parameters("dbName", dbName, "tableName", tableName));
            List<Map<String, Object>> resultList = result.list(record -> record.asMap());
            
            long endTime = System.currentTimeMillis();
            logger.info("Table-level query executed successfully in {} ms, returned {} tables", 
                       (endTime - startTime), resultList.size());
            
            return resultList;
        } catch (Exception e) {
            logger.error("Error executing table-level Neo4j query: {}", e.getMessage(), e);
            throw e;
        }
    }
} 