package com.example.controller;

import com.example.service.BloodRelationService;
import com.example.service.BloodRelationGraphFormatter;
import com.example.common.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/blood-relation")
public class BloodRelationController {

    @Autowired
    private BloodRelationService bloodRelationService;

    @GetMapping("/column-relation")
    public CommonResponse getColumnRelation(
            @RequestParam String labelName,
            @RequestParam String relName,
            @RequestParam String dbName,
            @RequestParam String tableName,
            @RequestParam(required = false) Integer columnId,
            @RequestParam Integer level,
            @RequestParam String flag,
            @RequestParam(required = false, defaultValue = "false") Boolean skipTempTable,
            @RequestParam(required = false, defaultValue = "LAYERED") String displayStrategy) {
        
        // 转换字符串策略为枚举
        BloodRelationGraphFormatter.DisplayStrategy strategy;
        try {
            strategy = BloodRelationGraphFormatter.DisplayStrategy.valueOf(displayStrategy.toUpperCase());
        } catch (IllegalArgumentException e) {
            strategy = BloodRelationGraphFormatter.DisplayStrategy.LAYERED; // 默认策略
        }
        
        return bloodRelationService.getColumnRelation(labelName, relName, dbName, tableName, columnId, level, flag, skipTempTable, strategy);
    }
}