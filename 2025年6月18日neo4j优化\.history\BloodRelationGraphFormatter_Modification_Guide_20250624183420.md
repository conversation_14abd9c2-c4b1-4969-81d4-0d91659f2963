# 血缘关系格式化器 (`BloodRelationGraphFormatter`) 增强修改说明

## 1. 背景与问题

当前，数据血缘关系的"大一统血缘关系树"在进行溯源查询 (`flag=1`) 时，其树结构的构建**过度依赖**上游传入数据中的 `level` 属性。

经过分析发现，在处理复杂的、包含网络状依赖的数据时，上游服务计算出的 `level` 值可能与实际的数据流加工顺序不符。这导致了一个核心问题：

当使用 `flag=1` 对一个已经是源头 (`Level 0`) 的表进行溯源查询时，格式化器无法正确构建以"最终产品"为根节点的溯源树，而是错误地仍以 `Level 0` 的表作为根节点，展现了一个"影响分析"的视图。这与用户"从最终结果追溯其来源"的期望不符。

## 2. 修改目标

本次修改的目标是：**在不改动上游服务（如 `BloodRelationService`）的前提下，增强 `BloodRelationGraphFormatter` 的智能性和健壮性**。

核心要求：当进行溯源查询 (`flag=1`) 时，渲染逻辑应**彻底忽略**不可靠的 `level` 属性，仅依据数据对象本身的关系（`ColumnDTO` 及其包含的 `RelationDTO` 列表）来构建一棵逻辑上绝对正确的溯源树。

最终，要实现用户期望的效果：
*   **溯源查询 (`flag=1`)**: 无论查询的起点是什么，都应以数据链路中**最下游的节点**（最终产品）作为树的根，将被查询的表/字段（`Level 0`）正确地展示在树的**叶子节点**位置。
*   **影响查询 (`flag=0`)**: 保持现有逻辑不变，以被查询的表/字段（`Level 0`）作为树的根，向下展示其影响范围。

## 3. 代码修改执行细则

本次修改将精准地作用于 `BloodRelationGraphFormatter.java` 文件中的内部类 `BloodRelationTreeBuilder`。

### 3.1 修改点一：父子关系判定规则 (`buildParentChildRelations` 方法)

*   **目的**: 修正溯源场景下父子关系的建立逻辑，使其不再依赖 `level` 值。
*   **定位**: `BloodRelationTreeBuilder` 类的 `buildParentChildRelations` 方法内部，处理 `flag="1"` 的代码块。

*   **原逻辑**:
    ```java
    // ...
    } else if ("1".equals(flag)) {
        // flag=1：溯源关系，Level大的是父节点，Level小的是子节点
        // 即：Level大 -> Level小
        if (relatedNode.getLevel() > currentNode.getLevel()) { // <-- 依赖 level 比较
            relatedNode.addChild(currentNode, /* ... */);
        } else if (relatedNode.getLevel() < currentNode.getLevel()) { // <-- 依赖 level 比较
            currentNode.addChild(relatedNode, /* ... */);
        }
    } 
    // ...
    ```

*   **修改后逻辑**:
    ```java
    // ...
    } else if ("1".equals(flag)) {
        // flag=1：溯源关系，无论 level 值是什么，持有关系(relation)的节点(currentNode)一定是下游，
        // 被关系指向的节点(relatedNode)一定是上游。
        // 在溯源树中，下游是父，上游是子。
        currentNode.addChild(relatedNode, 
                           relation.getRelJobName(), 
                           relation.getSqlNo(), 
                           relation.getSkippedTempTables());
    } 
    // ...
    ```
*   **说明**: 此修改强制规定了在溯源模式下，数据流的下游节点（`currentNode`）是父节点，上游节点（`relatedNode`）是子节点。这个规则基于数据本身的固有结构，完全屏蔽了错误 `level` 值带来的干扰。

### 3.2 修改点二：树的根节点判定规则 (`identifyRootNodesAndConvergencePoints` 方法)

*   **目的**: 修正溯源场景下根节点的查找逻辑，使其不再依赖 `level` 值。
*   **定位**: `BloodRelationTreeBuilder` 类的 `identifyRootNodesAndConvergencePoints` 方法内部，处理 `flag="1"` 的代码块。

*   **原逻辑**:
    ```java
    // ...
    } else if ("1".equals(flag)) {
        // flag=1：Level最大的节点作为实际根节点
        Integer maxLevel = nodeMap.values().stream() // <-- 依赖 level 查找最大值
                .mapToInt(TreeNode::getLevel)
                .max()
                .orElse(0);
        
        for (TreeNode node : nodeMap.values()) {
            if (node.getLevel().equals(maxLevel) && node.getParents().isEmpty()) { // <-- 依赖 level 比较
                actualRootNodes.add(node);
            }
        }
    } 
    // ...
    ```

*   **修改后逻辑**:
    ```java
    // ...
    } else if ("1".equals(flag)) {
        // flag=1: 溯源模式下，在构建完正确的父子关系后，
        // 真正的根节点是那些没有父节点的节点（即数据链路的最下游）。
        for (TreeNode node : nodeMap.values()) {
            if (node.getParents().isEmpty()) {
                actualRootNodes.add(node);
            }
        }
    } 
    // ...
    ```
*   **说明**: 此修改将根节点的判定标准从"`level` 值最大"，变更为"没有父节点的节点"。这是在第一步修改的基础上，对逻辑正确性的自然延伸，确保了无论 `level` 值如何，都能准确找到数据链路的真正终点作为树的根。

## 4. 预期结果

完成以上修改后，`BloodRelationGraphFormatter` 将具备以下行为：
*   当接收到 `flag=1` 的请求时，无论上游数据中的 `level` 值是否准确，都能稳定、正确地生成以数据链路最末端节点为根的**溯源树**。
*   当接收到 `flag=0` 或 `flag` 为 `null` 的请求时，行为保持不变，继续生成以查询目标为根的**影响分析树**。
*   整体应用的健壮性得到提升，对上游数据的微小逻辑错误具备了更强的"免疫力"。 