package com.example.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@
@ApiModel(value = "表级血缘关系信息", description = "数据表之间的血缘关系详细信息")
public class TableRelationDTO {
    
    @ApiModelProperty(value = "关系唯一标识ID", example = "1")
    private Integer relId;
    
    @ApiModelProperty(value = "数据处理作业名称", example = "etl_job_001")
    private String relJobName;
    
    @ApiModelProperty(value = "SQL脚本编号", example = "sql_001")
    private String sqlNo;
    
    @ApiModelProperty(value = "ELT系统标识", example = "kettle")
    private String eltSystem;
    
    @ApiModelProperty(value = "跳过的临时表列表（当启用skipTempTable时）", example = "[\"tmp_staging\", \"tmp_clean\"]")
    private List<String> skippedTempTables;

    
} 