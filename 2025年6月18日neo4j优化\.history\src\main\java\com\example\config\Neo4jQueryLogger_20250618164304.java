package com.example.config;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Neo4j查询日志拦截器
 * 模拟MyBatis的SQL日志输出功能
 */
@Aspect
@Component
public class Neo4jQueryLogger {

    private static final Logger logger = LoggerFactory.getLogger("NEO4J_QUERY");

    @Around("execution(* com.example.repository.*.*(..)) && " +
            "(execution(* *getColumnRel*(..)) || " +
            "execution(* *getColumnInfo*(..)) || " +
            "execution(* *getColumnsWithRelation*(..)))")
    public Object logQuery(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        logger.info("==>  Preparing Neo4j Query: {}", methodName);
        
        // 构建参数信息
        StringBuilder paramInfo = new StringBuilder("==>  Parameters: ");
        for (int i = 0; i < args.length; i++) {
            paramInfo.append(args[i]);
            if (i < args.length - 1) {
                paramInfo.append(", ");
            }
        }
        logger.info(paramInfo.toString());
        
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            long executeTime = endTime - startTime;
            
            if (result instanceof java.util.List) {
                java.util.List<?> list = (java.util.List<?>) result;
                logger.info("<==      Total: {} records, Execute Time: {} ms", list.size(), executeTime);
            } else {
                logger.info("<==      Execute Time: {} ms", executeTime);
            }
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executeTime = endTime - startTime;
            logger.error("<==      Error after {} ms: {}", executeTime, e.getMessage());
            throw e;
        }
    }
} 