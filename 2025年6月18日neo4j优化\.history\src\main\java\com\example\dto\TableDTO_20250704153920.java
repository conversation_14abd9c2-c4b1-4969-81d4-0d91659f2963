package com.example.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

@ApiModel(value = "数据表信息", description = "血缘关系中的表信息")
public class TableDTO {
    
    @ApiModelProperty(value = "表ID", example = "1")
    private Integer tableId;
    
    @ApiModelProperty(value = "字段ID（当前节点的字段ID）", example = "1001")
    private Integer columnId;
    
    @ApiModelProperty(value = "字段名称（当前节点的字段名）", example = "user_id")
    private String columnName;
    
    @ApiModelProperty(value = "表名称", example = "user_info")
    private String tableName;
    
    @ApiModelProperty(value = "数据库名称", example = "ods_db")
    private String dbName;
    
    @ApiModelProperty(value = "数据处理层级。0表示源数据层，1表示一级处理，数字越大表示处理层级越深", example = "0")
    private Integer level;
    
    @ApiModelProperty(value = "表中的字段列表，包含该表在血缘关系中涉及的所有字段（字段级血缘时使用）")
    private List<ColumnDTO> columns;
    
    @ApiModelProperty(value = "表级血缘关系列表，包含导致该表关联的所有表级关系（表级血缘时使用）")
    private List<TableRelationDTO> relations;

    public Integer getTableId() {
        return tableId;
    }

    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }

    public Integer getColumnId() {
        return columnId;
    }

    public void setColumnId(Integer columnId) {
        this.columnId = columnId;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public List<ColumnDTO> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnDTO> columns) {
        this.columns = columns;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<TableRelationDTO> getRelations() {
        return relations;
    }

    public void setRelations(List<TableRelationDTO> relations) {
        this.relations = relations;
    }
} 