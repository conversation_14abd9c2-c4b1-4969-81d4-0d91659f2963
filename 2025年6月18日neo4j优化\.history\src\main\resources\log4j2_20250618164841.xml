<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
        </Console>
        
        <!-- 文件输出 -->
        <RollingFile name="RollingFile" fileName="logs/app.log"
                     filePattern="logs/app-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="10MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <!-- Neo4j 查询日志专用输出 -->
        <Console name="Neo4jConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{HH:mm:ss.SSS} [Neo4j] %msg%n"/>
        </Console>
    </Appenders>
    
    <Loggers>
        <!-- Neo4j Driver 日志配置 -->
        <!-- 显示所有查询语句 -->
        <Logger name="org.neo4j.driver" level="DEBUG" additivity="false">
            <AppenderRef ref="Neo4jConsole"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <!-- 显示查询执行详情 -->
        <Logger name="org.neo4j.driver.internal" level="DEBUG" additivity="false">
            <AppenderRef ref="Neo4jConsole"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <!-- 显示网络通信日志（如果需要更详细的信息） -->
        <Logger name="org.neo4j.driver.internal.async" level="INFO" additivity="false">
            <AppenderRef ref="Neo4jConsole"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <!-- Spring 框架日志 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <!-- 您的应用程序日志 -->
        <Logger name="com.example" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <!-- Root Logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>
</Configuration> 