package com.example.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "通用响应结果", description = "系统统一返回格式")
public class CommonResponse<T> {
    
    @ApiModelProperty(value = "响应状态码，200表示成功", example = "200")
    private int code;
    
    @ApiModelProperty(value = "响应消息描述", example = "success")
    private String message;
    
    @ApiModelProperty(value = "响应数据内容。血缘关系查询时包含totalLevel（总层级数）和tableList（表列表）")
    private T data;

    public CommonResponse() {
        this.code = 200;
        this.message = "success";
    }

    public CommonResponse(T data) {
        this.code = 200;
        this.message = "success";
        this.data = data;
    }

    public CommonResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public CommonResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
} 