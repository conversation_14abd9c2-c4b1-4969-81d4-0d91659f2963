package com.example.service;

import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 血缘关系图形化格式化器
 * 将血缘关系数据转换为ASCII树状图，便于日志查看和理解
 */
@Component
public class BloodRelationGraphFormatter {
    
    private static final Logger logger = LoggerFactory.getLogger(BloodRelationGraphFormatter.class);
    
    /**
     * 血缘关系显示策略枚举
     */
    public enum DisplayStrategy {
        LAYERED,    // 分层显示策略
        UNIFIED_TREE // 大一统血缘树策略
    }
    
    // ASCII字符常量
    private static final String TREE_BRANCH = "├─ ";
    private static final String TREE_LAST = "└─ ";
    private static final String TREE_VERTICAL = "│  ";
    private static final String TREE_SPACE = "   ";
    private static final String FLOW_ARROW = " ━━━> ";
    private static final String BRANCH_DOWN = " ┳";
    private static final String BRANCH_MID = " ┣";
    private static final String BRANCH_LAST = " ┗";
    private static final String SKIP_ICON = "[跳过]";
    private static final String TABLE_ICON = "📊";
    
    /**
     * 格式化血缘关系并打印到日志
     */
    public void printBloodRelationGraph(List<TableDTO> tables, boolean skipTempTable, String flag) {
        printBloodRelationGraph(tables, skipTempTable, DisplayStrategy.LAYERED, flag);
    }
    
    /**
     * 格式化血缘关系并打印到日志（支持策略选择）
     */
    public void printBloodRelationGraph(List<TableDTO> tables, boolean skipTempTable, DisplayStrategy strategy, String flag) {
        if (CollectionUtils.isEmpty(tables)) {
            logger.info("血缘关系为空，无图可显示");
            return;
        }
        
        switch (strategy) {
            case LAYERED:
                // 分层显示策略
                String layeredGraphString = formatLayeredBloodRelationGraph(tables, skipTempTable);
                logger.info("\n=== 分层血缘关系图 ===\n" + layeredGraphString);
                
                // 可选：同时显示流向图
                String flowGraphString = formatBloodRelationFlowGraph(tables, skipTempTable);
                logger.info("\n=== 血缘流向图 ===\n" + flowGraphString);
                break;
                
            case UNIFIED_TREE:
                // 大一统血缘树策略
                String unifiedTreeString = formatUnifiedBloodRelationTree(tables, skipTempTable, flag);
                logger.info("\n=== 大一统血缘关系树 ===\n" + unifiedTreeString);
                break;
                
            default:
                logger.warn("未知的显示策略: {}, 使用默认分层显示", strategy);
                printBloodRelationGraph(tables, skipTempTable, DisplayStrategy.LAYERED, flag);
                break;
        }
    }
    
    /**
     * 格式化血缘关系并打印到日志（支持策略和flag选择）
     */
    public void printBloodRelationGraph(List<TableDTO> tables, boolean skipTempTable, DisplayStrategy strategy, String flag) {
        if (CollectionUtils.isEmpty(tables)) {
            logger.info("血缘关系为空，无图可显示");
            return;
        }
        
        switch (strategy) {
            case LAYERED:
                // 分层显示策略
                String layeredGraphString = formatLayeredBloodRelationGraph(tables, skipTempTable);
                logger.info("\n=== 分层血缘关系图 ===\n" + layeredGraphString);
                
                // 可选：同时显示流向图
                String flowGraphString = formatBloodRelationFlowGraph(tables, skipTempTable);
                logger.info("\n=== 血缘流向图 ===\n" + flowGraphString);
                break;
                
            case UNIFIED_TREE:
                // 大一统血缘树策略
                String unifiedTreeString = formatUnifiedBloodRelationTree(tables, skipTempTable, flag);
                logger.info("\n=== 大一统血缘关系树 ===\n" + unifiedTreeString);
                break;
                
            default:
                logger.warn("未知的显示策略: {}, 使用默认分层显示", strategy);
                printBloodRelationGraph(tables, skipTempTable, DisplayStrategy.LAYERED, flag);
                break;
        }
    }
    
    /**
     * 将血缘关系格式化为分层ASCII树状图
     */
    public String formatLayeredBloodRelationGraph(List<TableDTO> tables, boolean skipTempTable) {
        StringBuilder graph = new StringBuilder();
        
        // 添加标题和时间
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        graph.append("┌").append(StrUtil.repeat("─", 80)).append("┐\n");
        graph.append("│").append(centerText("数据血缘关系图 " + TABLE_ICON, 80)).append("│\n");
        graph.append("│").append(centerText("生成时间: " + currentTime, 80)).append("│\n");
        if (skipTempTable) {
            graph.append("│").append(centerText("已启用临时表跳过模式", 80)).append("│\n");
        }
        graph.append("└").append(StrUtil.repeat("─", 80)).append("┘\n\n");
        
        // 按层级分组并排序
        Map<Integer, List<TableDTO>> levelMap = tables.stream()
                .collect(Collectors.groupingBy(TableDTO::getLevel));
        
        List<Integer> sortedLevels = levelMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        // 构建关系图
        RelationGraphBuilder graphBuilder = new RelationGraphBuilder(tables);
        
        // 生成每个层级的内容
        for (int i = 0; i < sortedLevels.size(); i++) {
            Integer level = sortedLevels.get(i);
            List<TableDTO> levelTables = levelMap.get(level);
            
            // 层级标题
            String levelTitle = getLevelTitle(level);
            graph.append(levelTitle).append("\n");
            
            // 处理该层级的每个表
            for (int tableIndex = 0; tableIndex < levelTables.size(); tableIndex++) {
                TableDTO table = levelTables.get(tableIndex);
                boolean isLastTable = (tableIndex == levelTables.size() - 1);
                
                // 表名
                String tablePrefix = isLastTable ? TREE_LAST : TREE_BRANCH;
                graph.append(tablePrefix).append(TABLE_ICON).append(" ")
                      .append(table.getDbName()).append(".").append(table.getTableName()).append("\n");
                
                // 处理该表的字段
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    formatTableColumns(graph, table, graphBuilder, isLastTable, skipTempTable);
                }
            }
            
            // 层级之间添加空行
            if (i < sortedLevels.size() - 1) {
                graph.append("\n");
            }
        }
        
        // 添加统计信息
        graph.append("\n").append(generateSummary(tables, skipTempTable));
        
        return graph.toString();
    }
    
    /**
     * 格式化血缘流向图 - 显示level之间的连接关系
     */
    public String formatBloodRelationFlowGraph(List<TableDTO> tables, boolean skipTempTable) {
        StringBuilder graph = new StringBuilder();
        
        // 添加标题
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        graph.append("┌").append(StrUtil.repeat("─", 80)).append("┐\n");
        graph.append("│").append(centerText("数据血缘流向图 🔄", 80)).append("│\n");
        graph.append("│").append(centerText("生成时间: " + currentTime, 80)).append("│\n");
        if (skipTempTable) {
            graph.append("│").append(centerText("已启用临时表跳过模式", 80)).append("│\n");
        }
        graph.append("└").append(StrUtil.repeat("─", 80)).append("┘\n\n");
        
        // 构建level间的流向关系
        Map<Integer, List<TableDTO>> levelMap = tables.stream()
                .collect(Collectors.groupingBy(TableDTO::getLevel));
        
        List<Integer> sortedLevels = levelMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        if (sortedLevels.isEmpty()) {
            graph.append("无血缘关系数据\n");
            return graph.toString();
        }
        
        // 构建字段到level的映射
        Map<String, Integer> fieldToLevelMap = buildFieldToLevelMap(tables);
        
        // 显示每个level及其向下游的流向
        for (int i = 0; i < sortedLevels.size(); i++) {
            Integer currentLevel = sortedLevels.get(i);
            List<TableDTO> currentLevelTables = levelMap.get(currentLevel);
            
            // 当前level标题
            graph.append(getLevelFlowTitle(currentLevel)).append("\n");
            
            // 显示当前level的表和字段
            for (int tableIndex = 0; tableIndex < currentLevelTables.size(); tableIndex++) {
                TableDTO table = currentLevelTables.get(tableIndex);
                boolean isLastTable = (tableIndex == currentLevelTables.size() - 1);
                
                // 表信息
                String tablePrefix = isLastTable ? TREE_LAST : TREE_BRANCH;
                graph.append(tablePrefix).append(TABLE_ICON).append(" ")
                      .append(table.getDbName()).append(".").append(table.getTableName()).append("\n");
                
                // 字段及其流向
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    formatTableFieldsFlow(graph, table, fieldToLevelMap, isLastTable, i < sortedLevels.size() - 1);
                }
            }
            
            // level之间添加流向箭头
            if (i < sortedLevels.size() - 1) {
                graph.append("    ").append("⬇").append("\n");
                graph.append("    ").append("数据流向").append("\n");
                graph.append("    ").append("⬇").append("\n\n");
            }
        }
        
        return graph.toString();
    }
    
    /**
     * 构建字段到level的映射
     */
    private Map<String, Integer> buildFieldToLevelMap(List<TableDTO> tables) {
        Map<String, Integer> fieldToLevelMap = new HashMap<>();
        
        for (TableDTO table : tables) {
            if (!CollectionUtils.isEmpty(table.getColumns())) {
                for (ColumnDTO column : table.getColumns()) {
                    String fieldKey = column.getColumnId() + ":" + column.getColumnName();
                    fieldToLevelMap.put(fieldKey, table.getLevel());
                }
            }
        }
        
        return fieldToLevelMap;
    }
    
    /**
     * 格式化表字段的流向关系
     */
    private void formatTableFieldsFlow(StringBuilder graph, TableDTO table, 
                                      Map<String, Integer> fieldToLevelMap, 
                                      boolean isLastTable, boolean hasNextLevel) {
        List<ColumnDTO> columns = table.getColumns();
        
        for (int colIndex = 0; colIndex < columns.size(); colIndex++) {
            ColumnDTO column = columns.get(colIndex);
            boolean isLastColumn = (colIndex == columns.size() - 1);
            
            // 字段基础信息
            String columnPrefix = getColumnPrefix(isLastTable, isLastColumn);
            graph.append(columnPrefix)
                  .append(column.getColumnName())
                  .append("(").append(column.getColumnId()).append(")");
            
            // 显示该字段的下游流向
            if (hasNextLevel && !CollectionUtils.isEmpty(column.getRelations())) {
                formatFieldDownstreamFlow(graph, column, fieldToLevelMap, table.getLevel());
            } else {
                graph.append("\n");
            }
        }
    }
    
    /**
     * 格式化字段的下游流向
     */
    private void formatFieldDownstreamFlow(StringBuilder graph, ColumnDTO column, 
                                          Map<String, Integer> fieldToLevelMap, Integer currentLevel) {
        List<RelationDTO> relations = column.getRelations();
        
        // 找出指向下一级的关系
        List<RelationDTO> downstreamRelations = relations.stream()
                .filter(rel -> {
                    String targetFieldKey = rel.getColumnId() + ":" + rel.getColumnName();
                    Integer targetLevel = fieldToLevelMap.get(targetFieldKey);
                    return targetLevel != null && targetLevel > currentLevel;
                })
                .collect(Collectors.toList());
        
        if (downstreamRelations.isEmpty()) {
            graph.append("\n");
            return;
        }
        
        if (downstreamRelations.size() == 1) {
            // 单一下游流向
            RelationDTO relation = downstreamRelations.get(0);
            String targetFieldKey = relation.getColumnId() + ":" + relation.getColumnName();
            Integer targetLevel = fieldToLevelMap.get(targetFieldKey);
            
            graph.append(" ━━━▶ [Level ").append(targetLevel).append("] ")
                  .append(relation.getColumnName())
                  .append("(").append(relation.getColumnId()).append(")");
            
            // 显示作业信息
            if (relation.getRelJobName() != null) {
                graph.append(" [").append(relation.getRelJobName());
                if (relation.getSqlNo() != null) {
                    graph.append("/").append(relation.getSqlNo());
                }
                graph.append("]");
            }
            graph.append("\n");
            
        } else {
            // 多个下游流向
            graph.append(" ━━━┬▶ 多个下游:\n");
            
            for (int i = 0; i < downstreamRelations.size(); i++) {
                RelationDTO relation = downstreamRelations.get(i);
                String targetFieldKey = relation.getColumnId() + ":" + relation.getColumnName();
                Integer targetLevel = fieldToLevelMap.get(targetFieldKey);
                boolean isLast = (i == downstreamRelations.size() - 1);
                
                String prefix = isLast ? "       └▶ " : "       ├▶ ";
                graph.append(prefix).append("[Level ").append(targetLevel).append("] ")
                      .append(relation.getColumnName())
                      .append("(").append(relation.getColumnId()).append(")");
                
                // 显示作业信息
                if (relation.getRelJobName() != null) {
                    graph.append(" [").append(relation.getRelJobName());
                    if (relation.getSqlNo() != null) {
                        graph.append("/").append(relation.getSqlNo());
                    }
                    graph.append("]");
                }
                graph.append("\n");
            }
        }
    }
    
    /**
     * 获取流向图的层级标题
     */
    private String getLevelFlowTitle(Integer level) {
        String title;
        switch (level) {
            case 0:
                title = "📍 Level " + level + " (起始数据源)";
                break;
            case 1:
                title = "🔄 Level " + level + " (一级处理)";
                break;
            case 2:
                title = "⚙️ Level " + level + " (二级处理)";
                break;
            case 3:
                title = "📊 Level " + level + " (三级处理)";
                break;
            default:
                title = "🎯 Level " + level + " (" + level + "级处理)";
                break;
        }
        return "┌─ " + title + " ─┐";
    }
    
    /**
     * 格式化表的字段及其关系
     */
    private void formatTableColumns(StringBuilder graph, TableDTO table, 
                                   RelationGraphBuilder graphBuilder, boolean isLastTable, boolean skipTempTable) {
        List<ColumnDTO> columns = table.getColumns();
        
        for (int colIndex = 0; colIndex < columns.size(); colIndex++) {
            ColumnDTO column = columns.get(colIndex);
            boolean isLastColumn = (colIndex == columns.size() - 1);
            
            // 字段基础信息
            String columnPrefix = getColumnPrefix(isLastTable, isLastColumn);
            graph.append(columnPrefix)
                  .append(column.getColumnName())
                  .append("(").append(column.getColumnId()).append(")");
            
            // 处理该字段的关系
            if (!CollectionUtils.isEmpty(column.getRelations())) {
                formatColumnRelations(graph, column, isLastTable, isLastColumn, skipTempTable);
            } else {
                graph.append("\n");
            }
        }
    }
    
    /**
     * 格式化字段的血缘关系
     */
    private void formatColumnRelations(StringBuilder graph, ColumnDTO column, 
                                      boolean isLastTable, boolean isLastColumn, boolean skipTempTable) {
        List<RelationDTO> relations = column.getRelations();
        
        if (relations.size() == 1) {
            // 单一关系
            RelationDTO relation = relations.get(0);
            graph.append(FLOW_ARROW)
                  .append(relation.getColumnName())
                  .append("(").append(relation.getColumnId()).append(")");
            
            // 显示作业信息
            if (relation.getRelJobName() != null) {
                graph.append(" [").append(relation.getRelJobName());
                if (relation.getSqlNo() != null) {
                    graph.append("/").append(relation.getSqlNo());
                }
                graph.append("]");
            }
            graph.append("\n");
            
            // 显示跳过的临时表信息
            if (skipTempTable && relation.getSkippedTempTables() != null && !relation.getSkippedTempTables().isEmpty()) {
                String skipPrefix = getSkipPrefix(isLastTable, isLastColumn);
                graph.append(skipPrefix).append(SKIP_ICON).append(" 跳过临时表: ");
                graph.append(String.join(" → ", relation.getSkippedTempTables())).append("\n");
            }
            
        } else if (relations.size() > 1) {
            // 多分支关系
            graph.append(BRANCH_DOWN).append("━━━> 多个去向:\n");
            
            for (int relIndex = 0; relIndex < relations.size(); relIndex++) {
                RelationDTO relation = relations.get(relIndex);
                boolean isLastRelation = (relIndex == relations.size() - 1);
                
                String relationPrefix = getRelationPrefix(isLastTable, isLastColumn, isLastRelation);
                graph.append(relationPrefix)
                      .append(relation.getColumnName())
                      .append("(").append(relation.getColumnId()).append(")");
                
                // 显示作业信息
                if (relation.getRelJobName() != null) {
                    graph.append(" [").append(relation.getRelJobName());
                    if (relation.getSqlNo() != null) {
                        graph.append("/").append(relation.getSqlNo());
                    }
                    graph.append("]");
                }
                graph.append("\n");
                
                // 显示跳过的临时表信息
                if (skipTempTable && relation.getSkippedTempTables() != null && !relation.getSkippedTempTables().isEmpty()) {
                    String skipPrefix = getSkipPrefixForMultiRelation(isLastTable, isLastColumn, isLastRelation);
                    graph.append(skipPrefix).append(SKIP_ICON).append(" 跳过临时表: ");
                    graph.append(String.join(" → ", relation.getSkippedTempTables())).append("\n");
                }
            }
        }
    }
    
    /**
     * 获取层级标题
     */
    private String getLevelTitle(Integer level) {
        String title;
        switch (level) {
            case 0:
                title = "Level " + level + ": 起始节点";
                break;
            case 1:
                title = "Level " + level + ": 一级血缘关系";
                break;
            case 2:
                title = "Level " + level + ": 二级血缘关系";
                break;
            case 3:
                title = "Level " + level + ": 三级血缘关系";
                break;
            default:
                title = "Level " + level + ": " + level + "级血缘关系";
                break;
        }
        return "┌─ " + title + " ─┐";
    }
    
    /**
     * 获取字段前缀
     */
    private String getColumnPrefix(boolean isLastTable, boolean isLastColumn) {
        if (isLastTable) {
            return isLastColumn ? TREE_SPACE + TREE_LAST : TREE_SPACE + TREE_BRANCH;
        } else {
            return isLastColumn ? TREE_VERTICAL + TREE_LAST : TREE_VERTICAL + TREE_BRANCH;
        }
    }
    
    /**
     * 获取关系前缀
     */
    private String getRelationPrefix(boolean isLastTable, boolean isLastColumn, boolean isLastRelation) {
        String base = isLastTable ? TREE_SPACE : TREE_VERTICAL;
        String columnBase = isLastColumn ? TREE_SPACE : TREE_VERTICAL;
        
        if (isLastRelation) {
            return base + columnBase + TREE_LAST;
        } else {
            return base + columnBase + TREE_BRANCH;
        }
    }
    
    /**
     * 获取跳过临时表信息的前缀（单一关系）
     */
    private String getSkipPrefix(boolean isLastTable, boolean isLastColumn) {
        if (isLastTable) {
            return isLastColumn ? TREE_SPACE + TREE_SPACE : TREE_SPACE + TREE_VERTICAL;
        } else {
            return isLastColumn ? TREE_VERTICAL + TREE_SPACE : TREE_VERTICAL + TREE_VERTICAL;
        }
    }
    
    /**
     * 获取跳过临时表信息的前缀（多分支关系）
     */
    private String getSkipPrefixForMultiRelation(boolean isLastTable, boolean isLastColumn, boolean isLastRelation) {
        String base = isLastTable ? TREE_SPACE : TREE_VERTICAL;
        String columnBase = isLastColumn ? TREE_SPACE : TREE_VERTICAL;
        String relationBase = isLastRelation ? TREE_SPACE : TREE_VERTICAL;
        
        return base + columnBase + relationBase + TREE_SPACE;
    }
    
    /**
     * 生成统计摘要
     */
    private String generateSummary(List<TableDTO> tables, boolean skipTempTable) {
        StringBuilder summary = new StringBuilder();
        
        // 计算统计数据
        int totalTables = tables.size();
        int totalColumns = tables.stream().mapToInt(t -> 
                CollectionUtils.isEmpty(t.getColumns()) ? 0 : t.getColumns().size()).sum();
        int totalRelations = tables.stream().mapToInt(t -> 
                CollectionUtils.isEmpty(t.getColumns()) ? 0 : 
                t.getColumns().stream().mapToInt(c -> 
                        CollectionUtils.isEmpty(c.getRelations()) ? 0 : c.getRelations().size()).sum()).sum();
        int maxLevel = tables.stream().mapToInt(TableDTO::getLevel).max().orElse(0) + 1;
        
        // 计算跳过的临时表数量
        int totalSkippedTempTables = 0;
        for (TableDTO table : tables) {
            if (!CollectionUtils.isEmpty(table.getColumns())) {
                for (ColumnDTO column : table.getColumns()) {
                    if (!CollectionUtils.isEmpty(column.getRelations())) {
                        for (RelationDTO relation : column.getRelations()) {
                            if (relation.getSkippedTempTables() != null) {
                                totalSkippedTempTables += relation.getSkippedTempTables().size();
                            }
                        }
                    }
                }
            }
        }
        
        // 构建摘要
        summary.append("╔").append(StrUtil.repeat("═", 60)).append("╗\n");
        summary.append("║").append(centerText("数据血缘关系统计摘要", 60)).append("║\n");
        summary.append("╠").append(StrUtil.repeat("═", 60)).append("╣\n");
        summary.append("║ 涉及数据表总数: ").append(String.format("%-45s", totalTables + "个")).append("║\n");
        summary.append("║ 涉及字段总数: ").append(String.format("%-47s", totalColumns + "个")).append("║\n");
        summary.append("║ 血缘关系总数: ").append(String.format("%-47s", totalRelations + "条")).append("║\n");
        summary.append("║ 数据处理层级: ").append(String.format("%-47s", maxLevel + "层")).append("║\n");
        
        if (skipTempTable) {
            summary.append("║ 临时表跳过: ").append(String.format("%-49s", "已启用")).append("║\n");
            if (totalSkippedTempTables > 0) {
                summary.append("║ 跳过临时表总数: ").append(String.format("%-43s", totalSkippedTempTables + "个")).append("║\n");
            }
        }
        
        summary.append("║").append(StrUtil.repeat(" ", 60)).append("║\n");
        summary.append("║ 说明: 数据从Level 0开始，经过多层处理，最终到达报表层 ").append("║\n");
        if (skipTempTable && totalSkippedTempTables > 0) {
            summary.append("║ 提示: 该关系跳过了中间的临时表，直接连接最终目标表 ").append("║\n");
        }
        summary.append("╚").append(StrUtil.repeat("═", 60)).append("╝");
        
        return summary.toString();
    }
    
    /**
     * 文本居中
     */
    private String centerText(String text, int width) {
        if (text.length() >= width) {
            return text.substring(0, width);
        }
        int padding = (width - text.length()) / 2;
        return StrUtil.repeat(" ", padding) + text + StrUtil.repeat(" ", width - text.length() - padding);
    }
    
    /**
     * 格式化大一统血缘关系树
     */
    public String formatUnifiedBloodRelationTree(List<TableDTO> tables, boolean skipTempTable) {
        return formatUnifiedBloodRelationTree(tables, skipTempTable, null);
    }
    
    /**
     * 格式化大一统血缘关系树
     */
    public String formatUnifiedBloodRelationTree(List<TableDTO> tables, boolean skipTempTable, String flag) {
        StringBuilder graph = new StringBuilder();
        
        // 添加标题
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        graph.append("┌").append(StrUtil.repeat("─", 80)).append("┐\n");
        graph.append("│").append(centerText("大一统血缘关系树 🌳", 80)).append("│\n");
        graph.append("│").append(centerText("生成时间: " + currentTime, 80)).append("│\n");
        if (skipTempTable) {
            graph.append("│").append(centerText("已启用临时表跳过模式", 80)).append("│\n");
        }
        
        // 根据flag添加方向说明
        String directionInfo = getDirectionInfo(flag);
        if (directionInfo != null) {
            graph.append("│").append(centerText(directionInfo, 80)).append("│\n");
        }
        
        graph.append("└").append(StrUtil.repeat("─", 80)).append("┘\n\n");
        
        // 构建统一的血缘关系树
        BloodRelationTreeBuilder treeBuilder = new BloodRelationTreeBuilder(tables, flag);
        List<TreeNode> rootNodes = treeBuilder.buildUnifiedTree();
        
        if (rootNodes.isEmpty()) {
            graph.append("无血缘关系数据\n");
            return graph.toString();
        }
        
        // 重置访问状态，准备显示
        treeBuilder.resetVisitedStatus();
        
        // 格式化树结构
        for (int i = 0; i < rootNodes.size(); i++) {
            TreeNode rootNode = rootNodes.get(i);
            boolean isLastRoot = (i == rootNodes.size() - 1);
            formatTreeNode(graph, rootNode, "", isLastRoot, skipTempTable, flag);
        }
        
        // 显示完成后重置访问状态，避免影响后续操作
        treeBuilder.resetVisitedStatus();
        
        // 添加统计信息
        graph.append("\n").append(generateUnifiedTreeSummary(tables, treeBuilder, skipTempTable));
        
        return graph.toString();
    }
    
    /**
     * 格式化树节点
     */
    private void formatTreeNode(StringBuilder graph, TreeNode node, String prefix, boolean isLast, boolean skipTempTable, String flag) {
        // 检查是否已访问过此节点
        if (node.isVisited()) {
            // 对于已访问的节点，显示引用标记
            String currentPrefix = isLast ? TREE_LAST : TREE_BRANCH;
            graph.append(prefix).append(currentPrefix).append("↗ 引用: ")
                  .append(node.getTableName()).append(".").append(node.getColumnName())
                  .append("(").append(node.getColumnId()).append(")")
                  .append(" [已在上方显示]").append("\n");
            return;
        }
        
        // 标记为已访问
        node.setVisited(true);
        
        // 当前节点信息
        String currentPrefix = isLast ? TREE_LAST : TREE_BRANCH;
        
        // 虚拟根节点特殊处理
        if (node.isVirtualRoot()) {
            graph.append(prefix).append(currentPrefix).append("📋 ").append(node.getColumnName()).append("\n");
        } else {
            // 普通节点显示
            graph.append(prefix).append(currentPrefix).append(TABLE_ICON).append(" ")
                  .append(node.getTableName()).append(".").append(node.getColumnName())
                  .append("(").append(node.getColumnId()).append(")");
            
            // 根据flag和Level显示标识
            if (node.getLevel() != null) {
                if (node.getLevel() == 0) {
                    graph.append(" [Level ").append(node.getLevel()).append(" - 当前查询表字段]");
                } else {
                    graph.append(" [Level ").append(node.getLevel()).append("]");
                }
            }
            
            // 显示汇聚信息
            if (node.isConvergencePoint()) {
                graph.append(" ← (汇聚点，").append(node.getParents().size()).append("个来源)");
            }
        }
        
        graph.append("\n");
        
        // 递归处理子节点
        List<TreeNode> children = node.getChildren();
        if (!CollectionUtils.isEmpty(children)) {
            String childPrefix = prefix + (isLast ? TREE_SPACE : TREE_VERTICAL);
            for (int i = 0; i < children.size(); i++) {
                TreeNode child = children.get(i);
                boolean isLastChild = (i == children.size() - 1);
                
                // 显示到子节点的关系信息
                RelationEdge edge = child.getEdgeFromParent(node);
                if (edge != null && edge.getJobName() != null) {
                    // 根据flag显示不同的关系描述
                    String relationDescription = getRelationDescription(flag);
                    String relationPrefix = childPrefix + (isLastChild ? TREE_SPACE : TREE_VERTICAL);
                    
                    graph.append(relationPrefix).append(relationDescription).append(edge.getJobName());
                    if (edge.getSqlNo() != null) {
                        graph.append("/").append(edge.getSqlNo());
                    }
                    graph.append("\n");
                    
                    // 显示跳过的临时表信息
                    if (skipTempTable && edge.getSkippedTempTables() != null && !edge.getSkippedTempTables().isEmpty()) {
                        graph.append(relationPrefix).append("  ").append(SKIP_ICON).append(" 跳过临时表: ");
                        graph.append(String.join(" → ", edge.getSkippedTempTables())).append("\n");
                    }
                }
                
                // 递归处理子节点
                formatTreeNode(graph, child, childPrefix, isLastChild, skipTempTable, flag);
            }
        }
    }
    
    /**
     * 根据flag获取关系描述
     */
    private String getRelationDescription(String flag) {
        if ("1".equals(flag)) {
            return "🔽 数据流向: ";
        } else if ("0".equals(flag)) {
            return "🔽 影响链路: ";
        } else {
            return "↓ 通过作业: ";
        }
    }
    
    /**
     * 生成大一统血缘树的统计摘要
     */
    private String generateUnifiedTreeSummary(List<TableDTO> tables, BloodRelationTreeBuilder treeBuilder, boolean skipTempTable) {
        StringBuilder summary = new StringBuilder();
        
        // 计算统计数据
        int totalNodes = treeBuilder.getTotalNodes();
        int rootNodes = treeBuilder.getRootNodes().size();
        int maxDepth = treeBuilder.getMaxDepth();
        int branchNodes = treeBuilder.getBranchNodes();
        int convergenceNodes = treeBuilder.getConvergenceNodes();
        int totalSkippedTempTables = treeBuilder.getTotalSkippedTempTables();
        
        // 构建摘要
        summary.append("┌").append(StrUtil.repeat("─", 80)).append("┐\n");
        summary.append("│").append(centerText("血缘关系树统计", 80)).append("│\n");
        summary.append("├").append(StrUtil.repeat("─", 80)).append("┤\n");
        summary.append("│ • 总节点数: ").append(String.format("%-63s", totalNodes + "个")).append("│\n");
        summary.append("│ • 根节点数: ").append(String.format("%-63s", rootNodes + "个")).append("│\n");
        summary.append("│ • 最大深度: ").append(String.format("%-63s", maxDepth + "层")).append("│\n");
        summary.append("│ • 分支节点: ").append(String.format("%-63s", branchNodes + "个")).append("│\n");
        summary.append("│ • 汇聚节点: ").append(String.format("%-63s", convergenceNodes + "个")).append("│\n");
        
        if (skipTempTable && totalSkippedTempTables > 0) {
            summary.append("│ • 跳过临时表: ").append(String.format("%-61s", totalSkippedTempTables + "个")).append("│\n");
        }
        
        summary.append("└").append(StrUtil.repeat("─", 80)).append("┘");
        
        return summary.toString();
    }
    
    /**
     * 获取方向说明信息
     */
    private String getDirectionInfo(String flag) {
        if ("0".equals(flag)) {
            return "被引用关系查询：展示当前字段的影响链路";
        } else if ("1".equals(flag)) {
            return "溯源关系查询：展示数据来源链路";
        }
        return null;
    }
    
    /**
     * 关系图构建器内部类
     */
    private static class RelationGraphBuilder {
        private final Map<String, Set<String>> relationMap;
        
        public RelationGraphBuilder(List<TableDTO> tables) {
            this.relationMap = new HashMap<>();
            buildRelationMap(tables);
        }
        
        private void buildRelationMap(List<TableDTO> tables) {
            // 构建字段间的关系映射，用于后续查找
            for (TableDTO table : tables) {
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    for (ColumnDTO column : table.getColumns()) {
                        String sourceKey = table.getDbName() + "." + table.getTableName() + "." + column.getColumnName();
                        
                        if (!CollectionUtils.isEmpty(column.getRelations())) {
                            Set<String> targets = column.getRelations().stream()
                                    .map(rel -> rel.getColumnName() + "(" + rel.getColumnId() + ")")
                                    .collect(Collectors.toSet());
                            relationMap.put(sourceKey, targets);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 树节点类
     */
    private static class TreeNode {
        private Integer columnId;
        private String columnName;
        private String tableName;
        private Integer level;
        private List<TreeNode> children;
        private List<TreeNode> parents;
        private boolean convergencePoint;
        private boolean visited; // 用于遍历时标记是否已访问
        
        // 关系边信息（从父节点到当前节点的关系信息）
        private Map<TreeNode, RelationEdge> parentEdges;
        
        public TreeNode(Integer columnId, String columnName, String tableName) {
            this.columnId = columnId;
            this.columnName = columnName;
            this.tableName = tableName;
            this.children = new ArrayList<>();
            this.parents = new ArrayList<>();
            this.convergencePoint = false;
            this.visited = false;
            this.parentEdges = new HashMap<>();
        }
        
        public TreeNode(Integer columnId, String columnName, String tableName, Integer level) {
            this.columnId = columnId;
            this.columnName = columnName;
            this.tableName = tableName;
            this.level = level;
            this.children = new ArrayList<>();
            this.parents = new ArrayList<>();
            this.convergencePoint = false;
            this.visited = false;
            this.parentEdges = new HashMap<>();
        }
        
        public void addChild(TreeNode child, String jobName, String sqlNo, List<String> skippedTempTables) {
            if (!this.children.contains(child)) {
                this.children.add(child);
            }
            if (!child.parents.contains(this)) {
                child.parents.add(this);
            }
            
            // 存储关系边信息（只有在不是虚拟连接时才创建边信息）
            if (jobName != null || sqlNo != null || skippedTempTables != null) {
                RelationEdge edge = new RelationEdge(jobName, sqlNo, skippedTempTables);
                child.parentEdges.put(this, edge);
            }
        }
        
        // Getters and Setters
        public Integer getColumnId() { return columnId; }
        public String getColumnName() { return columnName; }
        public String getTableName() { return tableName; }
        public Integer getLevel() { return level; }
        public void setLevel(Integer level) { this.level = level; }
        public List<TreeNode> getChildren() { return children; }
        public List<TreeNode> getParents() { return parents; }
        public boolean isConvergencePoint() { return convergencePoint; }
        public void setConvergencePoint(boolean convergencePoint) { this.convergencePoint = convergencePoint; }
        public boolean isVisited() { return visited; }
        public void setVisited(boolean visited) { this.visited = visited; }
        public Map<TreeNode, RelationEdge> getParentEdges() { return parentEdges; }
        
        // 获取从指定父节点的关系信息
        public RelationEdge getEdgeFromParent(TreeNode parent) {
            return parentEdges.get(parent);
        }
        
        // 判断是否是虚拟根节点
        public boolean isVirtualRoot() {
            return columnId != null && columnId == -1;
        }
    }
    
    /**
     * 关系边信息类
     */
    private static class RelationEdge {
        private String jobName;
        private String sqlNo;
        private List<String> skippedTempTables;
        
        public RelationEdge(String jobName, String sqlNo, List<String> skippedTempTables) {
            this.jobName = jobName;
            this.sqlNo = sqlNo;
            this.skippedTempTables = skippedTempTables;
        }
        
        public String getJobName() { return jobName; }
        public String getSqlNo() { return sqlNo; }
        public List<String> getSkippedTempTables() { return skippedTempTables; }
    }
    
    /**
     * 血缘关系树构建器
     */
    private static class BloodRelationTreeBuilder {
        private final List<TableDTO> tables;
        private final String flag;
        private final Map<String, TreeNode> nodeMap;
        private List<TreeNode> rootNodes;
        private int totalNodes;
        private int maxDepth;
        private int branchNodes;
        private int convergenceNodes;
        private int totalSkippedTempTables;
        
        public BloodRelationTreeBuilder(List<TableDTO> tables, String flag) {
            this.tables = tables;
            this.flag = flag;
            this.nodeMap = new HashMap<>();
            this.rootNodes = new ArrayList<>();
        }
        
        public List<TreeNode> buildUnifiedTree() {
            // 1. 创建所有节点
            createAllNodes();
            
            // 2. 建立父子关系
            buildParentChildRelations();
            
            // 3. 识别根节点和汇聚点
            identifyRootNodesAndConvergencePoints();
            
            // 4. 计算统计信息
            calculateStatistics();
            
            return rootNodes;
        }
        
        private void createAllNodes() {
            for (TableDTO table : tables) {
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    for (ColumnDTO column : table.getColumns()) {
                        String nodeKey = column.getColumnId() + ":" + column.getColumnName();
                        String tableName = table.getDbName() + "." + table.getTableName();
                        
                        TreeNode node = new TreeNode(column.getColumnId(), column.getColumnName(), tableName, table.getLevel());
                        nodeMap.put(nodeKey, node);
                    }
                }
            }
        }
        
        private void buildParentChildRelations() {
            for (TableDTO table : tables) {
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    for (ColumnDTO column : table.getColumns()) {
                        String currentKey = column.getColumnId() + ":" + column.getColumnName();
                        TreeNode currentNode = nodeMap.get(currentKey);
                        
                        if (currentNode != null && !CollectionUtils.isEmpty(column.getRelations())) {
                            for (RelationDTO relation : column.getRelations()) {
                                String relatedKey = relation.getColumnId() + ":" + relation.getColumnName();
                                TreeNode relatedNode = nodeMap.get(relatedKey);
                                
                                if (relatedNode != null) {
                                    // 根据flag决定父子关系的方向
                                    if ("0".equals(flag)) {
                                        // flag=0：被引用关系，Level小的是父节点，Level大的是子节点
                                        // relation指向的是源字段，current是目标字段
                                        // 源字段 -> 目标字段，即：Level小 -> Level大
                                        if (relatedNode.getLevel() < currentNode.getLevel()) {
                                            relatedNode.addChild(currentNode, 
                                                              relation.getRelJobName(), 
                                                              relation.getSqlNo(), 
                                                              relation.getSkippedTempTables());
                                        } else if (relatedNode.getLevel() > currentNode.getLevel()) {
                                            currentNode.addChild(relatedNode, 
                                                               relation.getRelJobName(), 
                                                               relation.getSqlNo(), 
                                                               relation.getSkippedTempTables());
                                        }
                                    } else if ("1".equals(flag)) {
                                        // flag=1：溯源关系，无论 level 值是什么，持有关系(relation)的节点(currentNode)一定是下游，
                                        // 被关系指向的节点(relatedNode)一定是上游。
                                        // 在溯源树中，下游是父，上游是子。
                                        currentNode.addChild(relatedNode, 
                                                           relation.getRelJobName(), 
                                                           relation.getSqlNo(), 
                                                           relation.getSkippedTempTables());
                                    } else {
                                        // 默认处理：简单的源字段 -> 目标字段关系
                                        relatedNode.addChild(currentNode, 
                                                          relation.getRelJobName(), 
                                                          relation.getSqlNo(), 
                                                          relation.getSkippedTempTables());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        private void identifyRootNodesAndConvergencePoints() {
            List<TreeNode> actualRootNodes = new ArrayList<>();
            
            // 根据flag识别不同的实际根节点
            if ("0".equals(flag)) {
                // flag=0：Level最小的节点作为实际根节点
                Integer minLevel = nodeMap.values().stream()
                        .mapToInt(TreeNode::getLevel)
                        .min()
                        .orElse(0);
                
                for (TreeNode node : nodeMap.values()) {
                    if (node.getLevel().equals(minLevel) && node.getParents().isEmpty()) {
                        actualRootNodes.add(node);
                    }
                }
            } else if ("1".equals(flag)) {
                // flag=1: 溯源模式下，在构建完正确的父子关系后，
                // 真正的根节点是那些没有父节点的节点（即数据链路的最下游）。
                for (TreeNode node : nodeMap.values()) {
                    if (node.getParents().isEmpty()) {
                        actualRootNodes.add(node);
                    }
                }
            } else {
                // 默认：没有父节点的节点作为实际根节点
                for (TreeNode node : nodeMap.values()) {
                    if (node.getParents().isEmpty()) {
                        actualRootNodes.add(node);
                    }
                }
            }
            
            // 创建虚拟根节点统一所有实际根节点
            if (actualRootNodes.size() > 1) {
                // 创建虚拟根节点
                String virtualRootTitle = getVirtualRootTitle(flag);
                TreeNode virtualRoot = new TreeNode(-1, virtualRootTitle, virtualRootTitle, -1);
                
                // 将所有实际根节点作为虚拟根节点的子节点
                for (TreeNode actualRoot : actualRootNodes) {
                    virtualRoot.addChild(actualRoot, null, null, null);
                }
                
                rootNodes.add(virtualRoot);
            } else {
                // 只有一个根节点时，直接使用
                rootNodes.addAll(actualRootNodes);
            }
            
            // 识别汇聚点（有多个父节点的节点）
            for (TreeNode node : nodeMap.values()) {
                if (node.getParents().size() > 1) {
                    node.setConvergencePoint(true);
                }
            }
        }
        
        /**
         * 获取虚拟根节点标题
         */
        private String getVirtualRootTitle(String flag) {
            if ("0".equals(flag)) {
                return "影响关系总览";
            } else if ("1".equals(flag)) {
                return "血缘溯源总览";
            }
            return "血缘关系总览";
        }
        
        private void calculateStatistics() {
            totalNodes = nodeMap.size();
            maxDepth = calculateMaxDepth();
            branchNodes = calculateBranchNodes();
            convergenceNodes = calculateConvergenceNodes();
            totalSkippedTempTables = calculateSkippedTempTables();
        }
        
        private int calculateMaxDepth() {
            int maxDepth = 0;
            
            // 重置所有节点的访问状态
            resetVisitedStatus();
            
            for (TreeNode root : rootNodes) {
                maxDepth = Math.max(maxDepth, calculateNodeDepth(root, 1));
            }
            
            // 重置访问状态
            resetVisitedStatus();
            
            return maxDepth;
        }
        
        private int calculateNodeDepth(TreeNode node, int currentDepth) {
            if (node.isVisited()) {
                return currentDepth; // 避免循环引用导致的无限递归
            }
            
            node.setVisited(true);
            
            if (node.getChildren().isEmpty()) {
                node.setVisited(false); // 回溯时重置状态
                return currentDepth;
            }
            
            int maxChildDepth = currentDepth;
            for (TreeNode child : node.getChildren()) {
                maxChildDepth = Math.max(maxChildDepth, calculateNodeDepth(child, currentDepth + 1));
            }
            
            node.setVisited(false); // 回溯时重置状态
            return maxChildDepth;
        }
        
        private int calculateBranchNodes() {
            int count = 0;
            for (TreeNode node : nodeMap.values()) {
                if (node.getChildren().size() > 1) {
                    count++;
                }
            }
            return count;
        }
        
        private int calculateConvergenceNodes() {
            int count = 0;
            for (TreeNode node : nodeMap.values()) {
                if (node.isConvergencePoint()) {
                    count++;
                }
            }
            return count;
        }
        
        private int calculateSkippedTempTables() {
            int count = 0;
            for (TreeNode node : nodeMap.values()) {
                for (RelationEdge edge : node.getParentEdges().values()) {
                    if (edge.getSkippedTempTables() != null) {
                        count += edge.getSkippedTempTables().size();
                    }
                }
            }
            return count;
        }
        
        // Getters for statistics
        public List<TreeNode> getRootNodes() { return rootNodes; }
        public int getTotalNodes() { return totalNodes; }
        public int getMaxDepth() { return maxDepth; }
        public int getBranchNodes() { return branchNodes; }
        public int getConvergenceNodes() { return convergenceNodes; }
        public int getTotalSkippedTempTables() { return totalSkippedTempTables; }
        
        // 公共方法，用于重置访问状态
        public void resetVisitedStatus() {
            for (TreeNode node : nodeMap.values()) {
                node.setVisited(false);
            }
        }
    }
    
    /**
     * 分析特定字段的完整关系链条
     * 
     * @param tables 血缘关系表数据
     * @param targetColumnId 目标字段ID（如3151992）
     * @param targetColumnName 目标字段名（如splitorg）
     * @param skipTempTable 是否跳过临时表
     * @return 格式化的关系链条分析结果
     */
    public String analyzeFieldRelationChain(List<TableDTO> tables, Integer targetColumnId, String targetColumnName, boolean skipTempTable) {
        StringBuilder analysis = new StringBuilder();
        
        // 添加标题
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        analysis.append("┌").append(StrUtil.repeat("═", 80)).append("┐\n");
        analysis.append("│").append(centerText("字段关系链条分析 🔍", 80)).append("│\n");
        analysis.append("│").append(centerText("目标字段: " + targetColumnName + "(" + targetColumnId + ")", 80)).append("│\n");
        analysis.append("│").append(centerText("生成时间: " + currentTime, 80)).append("│\n");
        if (skipTempTable) {
            analysis.append("│").append(centerText("已启用临时表跳过模式", 80)).append("│\n");
        }
        analysis.append("└").append(StrUtil.repeat("═", 80)).append("┘\n\n");
        
        // 找到目标字段的所有出现位置
        List<FieldOccurrence> occurrences = findFieldOccurrences(tables, targetColumnId, targetColumnName);
        
        if (occurrences.isEmpty()) {
            analysis.append("🚫 未找到字段 ").append(targetColumnName).append("(").append(targetColumnId).append(") 的任何出现记录\n");
            return analysis.toString();
        }
        
        // 按Level分组分析
        Map<Integer, List<FieldOccurrence>> levelOccurrences = occurrences.stream()
                .collect(Collectors.groupingBy(FieldOccurrence::getLevel));
        
        analysis.append("📊 **关系链条概览**\n");
        analysis.append("─────────────────────────────────────────────────────────────────────────────────\n");
        analysis.append("• 总出现次数: ").append(occurrences.size()).append(" 次\n");
        analysis.append("• 涉及层级: ").append(levelOccurrences.keySet().stream().sorted().map(String::valueOf).collect(Collectors.joining(", "))).append("\n");
        
        // 统计关系数量
        int totalRelations = occurrences.stream().mapToInt(occ -> occ.getRelations().size()).sum();
        analysis.append("• 总关系数: ").append(totalRelations).append(" 条\n\n");
        
        // 详细分析每个层级
        List<Integer> sortedLevels = levelOccurrences.keySet().stream().sorted().collect(Collectors.toList());
        
        for (int i = 0; i < sortedLevels.size(); i++) {
            Integer level = sortedLevels.get(i);
            List<FieldOccurrence> levelFields = levelOccurrences.get(level);
            
            analysis.append("🎯 **Level ").append(level).append(" 分析**\n");
            analysis.append("─────────────────────────────────────────────────────────────────────────────────\n");
            
            for (FieldOccurrence occurrence : levelFields) {
                analysis.append("📍 表位置: ").append(occurrence.getDbName()).append(".").append(occurrence.getTableName()).append("\n");
                
                if (occurrence.getRelations().isEmpty()) {
                    analysis.append("   └─ 无下游关系（可能是终点字段）\n");
                } else {
                    analysis.append("   └─ 下游关系 (").append(occurrence.getRelations().size()).append(" 条):\n");
                    
                    for (int relIndex = 0; relIndex < occurrence.getRelations().size(); relIndex++) {
                        RelationDTO relation = occurrence.getRelations().get(relIndex);
                        boolean isLastRel = (relIndex == occurrence.getRelations().size() - 1);
                        String prefix = isLastRel ? "      └─ " : "      ├─ ";
                        
                        analysis.append(prefix).append("➤ ")
                                .append(relation.getColumnName()).append("(").append(relation.getColumnId()).append(")");
                        
                        if (relation.getRelJobName() != null) {
                            analysis.append(" [作业: ").append(relation.getRelJobName());
                            if (relation.getSqlNo() != null) {
                                analysis.append("/").append(relation.getSqlNo());
                            }
                            analysis.append("]");
                        }
                        analysis.append("\n");
                        
                        // 显示跳过的临时表信息
                        if (skipTempTable && relation.getSkippedTempTables() != null && !relation.getSkippedTempTables().isEmpty()) {
                            String skipPrefix = isLastRel ? "         " : "      │  ";
                            analysis.append(skipPrefix).append("⚠️ 跳过临时表: ");
                            analysis.append(String.join(" → ", relation.getSkippedTempTables())).append("\n");
                        }
                    }
                }
                analysis.append("\n");
            }
        }
        
        // 关系链路分析
        analysis.append("🔗 **关系链路分析**\n");
        analysis.append("─────────────────────────────────────────────────────────────────────────────────\n");
        analyzeRelationPatterns(analysis, occurrences, targetColumnName, targetColumnId);
        
        // 影响范围分析
        analysis.append("\n📈 **影响范围分析**\n");
        analysis.append("─────────────────────────────────────────────────────────────────────────────────\n");
        analyzeImpactScope(analysis, occurrences, targetColumnName, targetColumnId);
        
        return analysis.toString();
    }
    
    /**
     * 找到目标字段的所有出现位置
     */
    private List<FieldOccurrence> findFieldOccurrences(List<TableDTO> tables, Integer targetColumnId, String targetColumnName) {
        List<FieldOccurrence> occurrences = new ArrayList<>();
        
        for (TableDTO table : tables) {
            if (!CollectionUtils.isEmpty(table.getColumns())) {
                for (ColumnDTO column : table.getColumns()) {
                    if (targetColumnId.equals(column.getColumnId()) && targetColumnName.equals(column.getColumnName())) {
                        FieldOccurrence occurrence = new FieldOccurrence();
                        occurrence.setColumnId(column.getColumnId());
                        occurrence.setColumnName(column.getColumnName());
                        occurrence.setDbName(table.getDbName());
                        occurrence.setTableName(table.getTableName());
                        occurrence.setLevel(table.getLevel());
                        occurrence.setRelations(column.getRelations() != null ? column.getRelations() : new ArrayList<>());
                        occurrences.add(occurrence);
                    }
                }
            }
        }
        
        return occurrences;
    }
    
    /**
     * 分析关系模式
     */
    private void analyzeRelationPatterns(StringBuilder analysis, List<FieldOccurrence> occurrences, String targetColumnName, Integer targetColumnId) {
        // 统计不同的目标字段
        Set<String> uniqueTargets = new HashSet<>();
        Set<String> uniqueJobs = new HashSet<>();
        Map<String, Integer> jobFrequency = new HashMap<>();
        
        for (FieldOccurrence occurrence : occurrences) {
            for (RelationDTO relation : occurrence.getRelations()) {
                String target = relation.getColumnName() + "(" + relation.getColumnId() + ")";
                uniqueTargets.add(target);
                
                if (relation.getRelJobName() != null) {
                    uniqueJobs.add(relation.getRelJobName());
                    jobFrequency.merge(relation.getRelJobName(), 1, Integer::sum);
                }
            }
        }
        
        analysis.append("• 影响的不同字段数: ").append(uniqueTargets.size()).append(" 个\n");
        analysis.append("• 涉及的作业数: ").append(uniqueJobs.size()).append(" 个\n");
        
        if (!jobFrequency.isEmpty()) {
            analysis.append("• 作业使用频率排行:\n");
            jobFrequency.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .limit(5)
                    .forEach(entry -> {
                        analysis.append("  ├─ ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" 次\n");
                    });
        }
        
        // 检查是否是汇聚点
        boolean isConvergencePoint = occurrences.stream()
                .anyMatch(occ -> occ.getRelations().size() > 1);
        
        if (isConvergencePoint) {
            analysis.append("• ⚠️ ").append(targetColumnName).append("(").append(targetColumnId).append(") 是一个**分支汇聚点**，具有多个下游去向\n");
        }
    }
    
    /**
     * 分析影响范围
     */
    private void analyzeImpactScope(StringBuilder analysis, List<FieldOccurrence> occurrences, String targetColumnName, Integer targetColumnId) {
        Set<String> affectedTables = new HashSet<>();
        Set<String> affectedDatabases = new HashSet<>();
        
        for (FieldOccurrence occurrence : occurrences) {
            affectedTables.add(occurrence.getDbName() + "." + occurrence.getTableName());
            affectedDatabases.add(occurrence.getDbName());
        }
        
        analysis.append("• 直接影响的数据库: ").append(affectedDatabases.size()).append(" 个 (").append(String.join(", ", affectedDatabases)).append(")\n");
        analysis.append("• 直接影响的数据表: ").append(affectedTables.size()).append(" 个\n");
        
        // 详细列出影响的表
        analysis.append("• 影响的表详情:\n");
        affectedTables.stream().sorted().forEach(table -> {
            analysis.append("  ├─ ").append(table).append("\n");
        });
        
        // 计算传播深度
        OptionalInt maxLevel = occurrences.stream().mapToInt(FieldOccurrence::getLevel).max();
        OptionalInt minLevel = occurrences.stream().mapToInt(FieldOccurrence::getLevel).min();
        
        if (maxLevel.isPresent() && minLevel.isPresent()) {
            int propagationDepth = maxLevel.getAsInt() - minLevel.getAsInt() + 1;
            analysis.append("• 数据传播深度: ").append(propagationDepth).append(" 层 (Level ")
                    .append(minLevel.getAsInt()).append(" 到 Level ").append(maxLevel.getAsInt()).append(")\n");
        }
    }
    
    /**
     * 字段出现位置信息类
     */
    private static class FieldOccurrence {
        private Integer columnId;
        private String columnName;
        private String dbName;
        private String tableName;
        private Integer level;
        private List<RelationDTO> relations;
        
        // Getters and Setters
        public Integer getColumnId() { return columnId; }
        public void setColumnId(Integer columnId) { this.columnId = columnId; }
        public String getColumnName() { return columnName; }
        public void setColumnName(String columnName) { this.columnName = columnName; }
        public String getDbName() { return dbName; }
        public void setDbName(String dbName) { this.dbName = dbName; }
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }
        public Integer getLevel() { return level; }
        public void setLevel(Integer level) { this.level = level; }
        public List<RelationDTO> getRelations() { return relations; }
        public void setRelations(List<RelationDTO> relations) { this.relations = relations; }
    }
} 