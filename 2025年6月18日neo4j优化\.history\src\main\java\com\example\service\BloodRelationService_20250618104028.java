package com.example.service;

import com.example.repository.BloodRelationRepository;
import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import com.example.common.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BloodRelationService {

    @Autowired
    private BloodRelationRepository bloodRelationRepository;

    public CommonResponse getColumnRelation(String labelName, String relName, Integer columnId, Integer level, String flag) {
        //处理表的情况
        List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, columnId, level, flag);
        List<TableDTO> tableDTOS = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        
        Map<String, Map<String, List<RelationDTO>>> dbTableMap = new HashMap<>();
        Map<String, Integer> dbTableLevelMap = new HashMap<>();
        
        //如果查不到，先回归表级别
        if (CollectionUtils.isEmpty(columnRels)) {
            List<Map<String, Object>> columnInfo = bloodRelationRepository.getColumnInfo(labelName, columnId);
            Map<String, Object> objectMap = columnInfo.get(0);
            Long startColumnIdLong = (Long) objectMap.get("startColumnId");
            Integer startColumnId = startColumnIdLong.intValue();
            String startColumnName = (String) objectMap.get("startColumnName");
            String startColumnTableName = (String) objectMap.get("startColumnTableName");
            String startColumnDbName = (String) objectMap.get("startColumnDbName");
            TableDTO tableDTO = new TableDTO();
            tableDTO.setTableName(startColumnTableName);
            tableDTO.setDbName(startColumnDbName);
            List<ColumnDTO> columnDTOS = new ArrayList<>();
            ColumnDTO columnDTO = new ColumnDTO();
            columnDTO.setColumnId(startColumnId);
            columnDTO.setColumnName(startColumnName);
            List<RelationDTO> relationDTOS = new ArrayList<>();
            columnDTO.setRelations(relationDTOS);
            columnDTOS.add(columnDTO);
            tableDTO.setColumns(columnDTOS);
            tableDTOS.add(tableDTO);
        } else {
            for (int i = 0; i < columnRels.size(); i++) {
                Map<String, Object> objectMap = columnRels.get(i);
                // 表名表示表和对应的columnDTO关系中的主要关系，含于表名
                String startColumnDbName = (String) objectMap.get("startColumnDbName");
                String startColumnTableName = (String) objectMap.get("startColumnTableName");
                String startColumnName = (String) objectMap.get("startColumnName");
                String endColumnDbName = (String) objectMap.get("endColumnDbName");
                String endColumnTableName = (String) objectMap.get("endColumnTableName");
                String endColumnName = (String) objectMap.get("endColumnName");
                String relJobName = (String) objectMap.get("relJobName");
                String sqlNo = (String) objectMap.get("sqlNo");
                
                Long startColumnIdLong = (Long) objectMap.get("startColumnId");
                Integer startColumnId = startColumnIdLong.intValue();
                Long endColumnIdLong = (Long) objectMap.get("endColumnId");
                Integer endColumnId = endColumnIdLong.intValue();
                
                RelationDTO relationDTO = new RelationDTO();
                relationDTO.setColumnId(startColumnId);
                relationDTO.setColumnName(startColumnName);
                relationDTO.setRelJobName(relJobName);
                relationDTO.setSqlNo(sqlNo);
                
                String dbTableName = endColumnDbName + "|" + endColumnTableName;
                String idName = endColumnId + "|" + endColumnName;
                
                //判断表层合并条件 判断表名包含是否有当前的关系，否则条件存入map里
                if (dbTableMap.containsKey(dbTableName)) {
                    Map<String, List<RelationDTO>> columnMap = dbTableMap.get(dbTableName);
                    //判断是否包含字段 如果包含就存字段相关关系
                    if (columnMap.containsKey(idName)) {
                        List<RelationDTO> relationDTOS = columnMap.get(idName);
                        //这不系统处理逻辑
                        relationDTOS.add(relationDTO);
                    } else {
                        List<RelationDTO> relationDTOS = new ArrayList<>();
                        //这不系统处理逻辑
                        relationDTOS.add(relationDTO);
                        columnMap.put(idName, relationDTOS);
                    }
                    dbTableMap.put(dbTableName, columnMap);
                } else {
                    Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                    List<RelationDTO> relationDTOS = new ArrayList<>();
                    //这不系统处理逻辑
                    relationDTOS.add(relationDTO);
                    columnMap.put(idName, relationDTOS);
                    dbTableMap.put(dbTableName, columnMap);
                }
                
                //数据处理完了
                //获取数据等级
                Long endColumnLevelLong = (Long) objectMap.get("endColumnLevel");
                Integer endColumnLevel = endColumnLevelLong.intValue();
                //获取本分存值数序小值
                if (dbTableLevelMap.containsKey(dbTableName)) {
                    Integer value = dbTableLevelMap.get(dbTableName);
                    if (endColumnLevel < value) {
                        dbTableLevelMap.put(dbTableName, endColumnLevel);
                    }
                } else {
                    dbTableLevelMap.put(dbTableName, endColumnLevel);
                }
                
                String startDbTableName = startColumnDbName + "|" + startColumnTableName;
                if (!dbTableMap.containsKey(startDbTableName)) {
                    List<RelationDTO> relationDTOS = new ArrayList<>();
                    Map<String, List<RelationDTO>> columnMap = new HashMap<>();
                    columnMap.put(idName, relationDTOS);
                    dbTableMap.put(startDbTableName, columnMap);
                }
                
                //数据处理等级的处理
                Long startColumnLevelLong = (Long) objectMap.get("startColumnLevel");
                Integer startColumnLevel = startColumnLevelLong.intValue();
                //获取本分存值数序小值
                if (dbTableLevelMap.containsKey(startDbTableName)) {
                    Integer value = dbTableLevelMap.get(startDbTableName);
                    if (startColumnLevel < value) {
                        dbTableLevelMap.put(startDbTableName, startColumnLevel);
                    }
                } else {
                    dbTableLevelMap.put(startDbTableName, startColumnLevel);
                }            
            }
            
            //数据获取等级的总数据
            List<Integer> dbTableLevelList = dbTableLevelMap.values().stream().distinct().sorted().collect(Collectors.toList());
            Map<String, Integer> dbTableLevelMapNew = new HashMap<>();
            dbTableLevelMap.forEach((k, v) -> {
                v = dbTableLevelList.indexOf(v);
                dbTableLevelMapNew.put(k, v);
            });
            
            //遍历map转换回给表体
            System.out.println(dbTableLevelList);
            System.out.println(dbTableLevelMapNew);
            //通过map转换回表体
            for (Map.Entry<String, Map<String, List<RelationDTO>>> dbTableEntry : dbTableMap.entrySet()) {
                String dbTableName = dbTableEntry.getKey();
                String[] split = dbTableName.split("\\|");
                TableDTO tableDTO = new TableDTO();
                tableDTO.setDbName(split[0]);
                tableDTO.setTableName(split[1]);
                List<ColumnDTO> columnDTOS = new ArrayList<>();
                Map<String, List<RelationDTO>> columnMap = dbTableEntry.getValue();
                for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
                    String idName = columnEntry.getKey();
                    String[] split1 = idName.split("\\|");
                    List<RelationDTO> relationDTOS = columnEntry.getValue();
                    ColumnDTO columnDTO = new ColumnDTO();
                    columnDTO.setColumnId(Integer.parseInt(split1[0]));
                    columnDTO.setColumnName(split1[1]);
                    columnDTO.setRelations(relationDTOS);
                    columnDTOS.add(columnDTO);
                }
                tableDTO.setColumns(columnDTOS);
                Integer value = dbTableLevelMapNew.get(dbTableName);
                tableDTO.setLevel(value);
                tableDTOS.add(tableDTO);
            }
            
            Integer totalLevel = 0;
            for (Map.Entry<String, Integer> dbTableLevelEntry : dbTableLevelMapNew.entrySet()) {
                Integer value = dbTableLevelEntry.getValue() + 1;
                if (value > totalLevel) {
                    totalLevel = value;
                }
            }
        }
        resultMap.put("totalLevel", totalLevel);
        resultMap.put("tableList", tableDTOS);
        return new CommonResponse<>(resultMap);
    }
} 