package com.example.service;

import com.example.repository.BloodRelationRepository;
import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import com.example.common.CommonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BloodRelationService {

    @Autowired
    private BloodRelationRepository bloodRelationRepository;

    public CommonResponse getColumnRelation(String labelName, String relName, Integer columnId, Integer level, String flag) {
        if (Objects.nonNull(columnId)) {
            // 查询单个字段的血缘关系
            return processSingleColumn(labelName, relName, columnId, level, flag);
        } else {
            // 查询整个表所有字段的血缘关系
            return processAllColumns(labelName, relName, level, flag);
        }
    }
    
    /**
     * 处理单个字段的血缘关系查询
     */
    private CommonResponse processSingleColumn(String labelName, String relName, Integer columnId, Integer level, String flag) {
        List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, columnId, level, flag);
        List<TableDTO> tableDTOS = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        
        if (CollectionUtils.isEmpty(columnRels)) {
            // 没有血缘关系时，返回字段自身信息
            handleEmptyRelations(labelName, columnId, tableDTOS, resultMap);
        } else {
            // 处理血缘关系
            Map<String, Map<String, List<RelationDTO>>> dbTableMap = new HashMap<>();
            Map<String, Integer> dbTableLevelMap = new HashMap<>();
            
            // 处理所有血缘关系
            processRelations(columnRels, dbTableMap, dbTableLevelMap);
            
            // 构建返回结果
            buildResult(dbTableMap, dbTableLevelMap, tableDTOS, resultMap);
        }
        
        resultMap.put("tableList", tableDTOS);
        return new CommonResponse<>(resultMap);
    }
    
    /**
     * 处理所有字段的血缘关系查询
     */
    private CommonResponse processAllColumns(String labelName, String relName, Integer level, String flag) {
        List<Map<String, Object>> allColumns = bloodRelationRepository.getAllColumnsByLabel(labelName);
        List<TableDTO> tableDTOS = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        
        if (CollectionUtils.isEmpty(allColumns)) {
            resultMap.put("totalLevel", 0);
            resultMap.put("tableList", tableDTOS);
            return new CommonResponse<>(resultMap);
        }
        
        // 全局聚合容器
        Map<String, Map<String, List<RelationDTO>>> globalDbTableMap = new HashMap<>();
        Map<String, Integer> globalDbTableLevelMap = new HashMap<>();
        
        // 遍历每个字段
        for (Map<String, Object> column : allColumns) {
            Long currentColumnIdLong = (Long) column.get("columnId");
            Integer currentColumnId = currentColumnIdLong.intValue();
            
            // 查询当前字段的血缘关系
            List<Map<String, Object>> columnRels = bloodRelationRepository.getColumnRel(labelName, relName, currentColumnId, level, flag);
            
            if (CollectionUtils.isEmpty(columnRels)) {
                // 确保没有血缘关系的字段也被包含在结果中
                ensureColumnIncluded(column, globalDbTableMap, globalDbTableLevelMap);
            } else {
                // 处理血缘关系并合并到全局容器
                processRelations(columnRels, globalDbTableMap, globalDbTableLevelMap);
            }
        }
        
        // 构建最终结果
        buildResult(globalDbTableMap, globalDbTableLevelMap, tableDTOS, resultMap);
        resultMap.put("tableList", tableDTOS);
        return new CommonResponse<>(resultMap);
    }
    
    /**
     * 处理空血缘关系的情况
     */
    private void handleEmptyRelations(String labelName, Integer columnId, List<TableDTO> tableDTOS, Map<String, Object> resultMap) {
        List<Map<String, Object>> columnInfo = bloodRelationRepository.getColumnInfo(labelName, columnId);
        Map<String, Object> objectMap = columnInfo.get(0);
        
        Long startColumnIdLong = (Long) objectMap.get("startColumnId");
        Integer startColumnId = startColumnIdLong.intValue();
        String startColumnName = (String) objectMap.get("startColumnName");
        String startColumnTableName = (String) objectMap.get("startColumnTableName");
        String startColumnDbName = (String) objectMap.get("startColumnDbName");
        
        TableDTO tableDTO = new TableDTO();
        tableDTO.setTableName(startColumnTableName);
        tableDTO.setDbName(startColumnDbName);
        
        List<ColumnDTO> columnDTOS = new ArrayList<>();
        ColumnDTO columnDTO = new ColumnDTO();
        columnDTO.setColumnId(startColumnId);
        columnDTO.setColumnName(startColumnName);
        columnDTOS.add(columnDTO);
        
        tableDTO.setColumns(columnDTOS);
        tableDTO.setLevel(0);
        tableDTOS.add(tableDTO);
        resultMap.put("totalLevel", 1);
    }
    
    /**
     * 确保没有血缘关系的字段也被包含在结果中
     */
    private void ensureColumnIncluded(Map<String, Object> column, 
                                    Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                    Map<String, Integer> dbTableLevelMap) {
        String columnName = (String) column.get("columnName");
        String tableName = (String) column.get("tableName");
        String dbName = (String) column.get("dbName");
        Long columnIdLong = (Long) column.get("columnId");
        Integer columnId = columnIdLong.intValue();
        
        String dbTableKey = dbName + "|" + tableName;
        String idName = columnId + "|" + columnName;
        
        dbTableMap.putIfAbsent(dbTableKey, new HashMap<>());
        dbTableMap.get(dbTableKey).putIfAbsent(idName, new ArrayList<>());
        dbTableLevelMap.putIfAbsent(dbTableKey, 0);
    }
    
    /**
     * 处理血缘关系数据
     */
    private void processRelations(List<Map<String, Object>> columnRels,
                                Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                Map<String, Integer> dbTableLevelMap) {
        for (Map<String, Object> objectMap : columnRels) {
            // 提取起始节点信息
            String startColumnDbName = (String) objectMap.get("startColumnDbName");
            String startColumnTableName = (String) objectMap.get("startColumnTableName");
            String startColumnName = (String) objectMap.get("startColumnName");
            Long startColumnIdLong = (Long) objectMap.get("startColumnId");
            Integer startColumnId = startColumnIdLong.intValue();
            Long startColumnLevelLong = (Long) objectMap.get("startColumnLevel");
            Integer startColumnLevel = startColumnLevelLong.intValue();
            
            // 提取结束节点信息
            String endColumnDbName = (String) objectMap.get("endColumnDbName");
            String endColumnTableName = (String) objectMap.get("endColumnTableName");
            String endColumnName = (String) objectMap.get("endColumnName");
            Long endColumnIdLong = (Long) objectMap.get("endColumnId");
            Integer endColumnId = endColumnIdLong.intValue();
            Long endColumnLevelLong = (Long) objectMap.get("endColumnLevel");
            Integer endColumnLevel = endColumnLevelLong.intValue();
            
            // 提取关系信息
            String relJobName = (String) objectMap.get("relJobName");
            String sqlNo = (String) objectMap.get("sqlNo");
            
            // 创建关系DTO
            RelationDTO relationDTO = new RelationDTO();
            relationDTO.setColumnId(startColumnId);
            relationDTO.setColumnName(startColumnName);
            relationDTO.setRelJobName(relJobName);
            relationDTO.setSqlNo(sqlNo);
            
            // 处理下游节点（终点）
            String endDbTableName = endColumnDbName + "|" + endColumnTableName;
            String endIdName = endColumnId + "|" + endColumnName;
            addRelationToMap(dbTableMap, endDbTableName, endIdName, relationDTO);
            updateTableLevel(dbTableLevelMap, endDbTableName, endColumnLevel);
            
            // 处理上游节点（起点）
            String startDbTableName = startColumnDbName + "|" + startColumnTableName;
            String startIdName = startColumnId + "|" + startColumnName;
            ensureTableAndColumnExists(dbTableMap, startDbTableName, startIdName);
            updateTableLevel(dbTableLevelMap, startDbTableName, startColumnLevel);
        }
    }
    
    /**
     * 添加关系到Map中
     */
    private void addRelationToMap(Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                String dbTableName, String idName, RelationDTO relationDTO) {
        dbTableMap.putIfAbsent(dbTableName, new HashMap<>());
        dbTableMap.get(dbTableName).putIfAbsent(idName, new ArrayList<>());
        dbTableMap.get(dbTableName).get(idName).add(relationDTO);
    }
    
    /**
     * 确保表和字段在Map中存在
     */
    private void ensureTableAndColumnExists(Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                                          String dbTableName, String idName) {
        dbTableMap.putIfAbsent(dbTableName, new HashMap<>());
        dbTableMap.get(dbTableName).putIfAbsent(idName, new ArrayList<>());
    }
    
    /**
     * 更新表的层级信息（保留最小层级）
     */
    private void updateTableLevel(Map<String, Integer> dbTableLevelMap, String dbTableName, Integer level) {
        dbTableLevelMap.merge(dbTableName, level, Math::min);
    }
    
    /**
     * 构建最终返回结果
     */
    private void buildResult(Map<String, Map<String, List<RelationDTO>>> dbTableMap,
                           Map<String, Integer> dbTableLevelMap,
                           List<TableDTO> tableDTOS,
                           Map<String, Object> resultMap) {
        // 层级归一化
        List<Integer> dbTableLevelList = dbTableLevelMap.values().stream()
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        
        Map<String, Integer> dbTableLevelMapNew = new HashMap<>();
        dbTableLevelMap.forEach((k, v) -> {
            dbTableLevelMapNew.put(k, dbTableLevelList.indexOf(v));
        });
        
        System.out.println(dbTableLevelList);
        System.out.println(dbTableLevelMapNew);
        
        // 构建TableDTO列表
        for (Map.Entry<String, Map<String, List<RelationDTO>>> dbTableEntry : dbTableMap.entrySet()) {
            String dbTableName = dbTableEntry.getKey();
            String[] split = dbTableName.split("\\|");
            
            TableDTO tableDTO = new TableDTO();
            tableDTO.setDbName(split[0]);
            tableDTO.setTableName(split[1]);
            
            // 构建ColumnDTO列表
            List<ColumnDTO> columnDTOS = new ArrayList<>();
            Map<String, List<RelationDTO>> columnMap = dbTableEntry.getValue();
            for (Map.Entry<String, List<RelationDTO>> columnEntry : columnMap.entrySet()) {
                String idName = columnEntry.getKey();
                String[] split1 = idName.split("\\|");
                
                ColumnDTO columnDTO = new ColumnDTO();
                columnDTO.setColumnId(Integer.parseInt(split1[0]));
                columnDTO.setColumnName(split1[1]);
                columnDTO.setRelations(columnEntry.getValue());
                columnDTOS.add(columnDTO);
            }
            
            tableDTO.setColumns(columnDTOS);
            tableDTO.setLevel(dbTableLevelMapNew.getOrDefault(dbTableName, 0));
            tableDTOS.add(tableDTO);
        }
        
        // 计算最大层级
        Integer totalLevel = dbTableLevelMapNew.values().stream()
                .mapToInt(v -> v + 1)
                .max()
                .orElse(0);
        
        resultMap.put("totalLevel", totalLevel);
    }
} 