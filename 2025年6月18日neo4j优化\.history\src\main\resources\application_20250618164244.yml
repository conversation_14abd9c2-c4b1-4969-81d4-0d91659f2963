server:
  port: 8080

spring:
  application:
    name: neo4j-blood-relation

# Neo4j配置
neo4j:
  uri: bolt://localhost:7687
  username: neo4j
  password: password

# 日志配置
logging:
  level:
    org.neo4j.driver: DEBUG          # Neo4j驱动日志
    org.neo4j.driver.internal: DEBUG # Neo4j内部日志
    com.example: DEBUG                # 应用程序日志
    org.springframework.data.neo4j: DEBUG # Spring Data Neo4j日志
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Neo4j配置
spring:
  neo4j:
    uri: bolt://localhost:7687
    authentication:
      username: neo4j
      password: your_password
    # 启用查询日志
    config:
      # 显示查询语句
      connection.liveness.check.timeout: 60000
      # 启用驱动日志
      logging.level: DEBUG 