package com.example.service;

import com.example.dto.TableDTO;
import com.example.dto.ColumnDTO;
import com.example.dto.RelationDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 血缘关系图形化格式化器
 * 将血缘关系数据转换为ASCII树状图，便于日志查看和理解
 */
@Component
public class BloodRelationGraphFormatter {
    
    private static final Logger logger = LoggerFactory.getLogger(BloodRelationGraphFormatter.class);
    
    // ASCII字符常量
    private static final String TREE_BRANCH = "├─ ";
    private static final String TREE_LAST = "└─ ";
    private static final String TREE_VERTICAL = "│  ";
    private static final String TREE_SPACE = "   ";
    private static final String FLOW_ARROW = " ━━━> ";
    private static final String BRANCH_DOWN = " ┳";
    private static final String BRANCH_MID = " ┣";
    private static final String BRANCH_LAST = " ┗";
    private static final String SKIP_ICON = "[跳过]";
    private static final String TABLE_ICON = "📊";
    
    /**
     * 格式化血缘关系并打印到日志
     */
    public void printBloodRelationGraph(List<TableDTO> tables, boolean skipTempTable) {
        if (CollectionUtils.isEmpty(tables)) {
            logger.info("血缘关系为空，无图可显示");
            return;
        }
        
        String graphString = formatBloodRelationGraph(tables, skipTempTable);
        logger.info("\n" + graphString);
    }
    
    /**
     * 将血缘关系格式化为ASCII树状图
     */
    public String formatBloodRelationGraph(List<TableDTO> tables, boolean skipTempTable) {
        StringBuilder graph = new StringBuilder();
        
        // 添加标题和时间
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        graph.append("┌").append(StrUtil.repeat("─", 80)).append("┐\n");
        graph.append("│").append(centerText("数据血缘关系图 " + TABLE_ICON, 80)).append("│\n");
        graph.append("│").append(centerText("生成时间: " + currentTime, 80)).append("│\n");
        if (skipTempTable) {
            graph.append("│").append(centerText("已启用临时表跳过模式", 80)).append("│\n");
        }
        graph.append("└").append(StrUtil.repeat("─", 80)).append("┘\n\n");
        
        // 按层级分组并排序
        Map<Integer, List<TableDTO>> levelMap = tables.stream()
                .collect(Collectors.groupingBy(TableDTO::getLevel));
        
        List<Integer> sortedLevels = levelMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        
        // 构建关系图
        RelationGraphBuilder graphBuilder = new RelationGraphBuilder(tables);
        
        // 生成每个层级的内容
        for (int i = 0; i < sortedLevels.size(); i++) {
            Integer level = sortedLevels.get(i);
            List<TableDTO> levelTables = levelMap.get(level);
            
            // 层级标题
            String levelTitle = getLevelTitle(level);
            graph.append(levelTitle).append("\n");
            
            // 处理该层级的每个表
            for (int tableIndex = 0; tableIndex < levelTables.size(); tableIndex++) {
                TableDTO table = levelTables.get(tableIndex);
                boolean isLastTable = (tableIndex == levelTables.size() - 1);
                
                // 表名
                String tablePrefix = isLastTable ? TREE_LAST : TREE_BRANCH;
                graph.append(tablePrefix).append(TABLE_ICON).append(" ")
                      .append(table.getDbName()).append(".").append(table.getTableName()).append("\n");
                
                // 处理该表的字段
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    formatTableColumns(graph, table, graphBuilder, isLastTable);
                }
            }
            
            // 层级之间添加空行
            if (i < sortedLevels.size() - 1) {
                graph.append("\n");
            }
        }
        
        // 添加统计信息
        graph.append("\n").append(generateSummary(tables, skipTempTable));
        
        return graph.toString();
    }
    
    /**
     * 格式化表的字段及其关系
     */
    private void formatTableColumns(StringBuilder graph, TableDTO table, 
                                   RelationGraphBuilder graphBuilder, boolean isLastTable) {
        List<ColumnDTO> columns = table.getColumns();
        
        for (int colIndex = 0; colIndex < columns.size(); colIndex++) {
            ColumnDTO column = columns.get(colIndex);
            boolean isLastColumn = (colIndex == columns.size() - 1);
            
            // 字段基础信息
            String columnPrefix = getColumnPrefix(isLastTable, isLastColumn);
            graph.append(columnPrefix)
                  .append(column.getColumnName())
                  .append("(").append(column.getColumnId()).append(")");
            
            // 处理该字段的关系
            if (!CollectionUtils.isEmpty(column.getRelations())) {
                formatColumnRelations(graph, column, isLastTable, isLastColumn);
            } else {
                graph.append("\n");
            }
        }
    }
    
    /**
     * 格式化字段的血缘关系
     */
    private void formatColumnRelations(StringBuilder graph, ColumnDTO column, 
                                      boolean isLastTable, boolean isLastColumn) {
        List<RelationDTO> relations = column.getRelations();
        
        if (relations.size() == 1) {
            // 单一关系
            RelationDTO relation = relations.get(0);
            graph.append(FLOW_ARROW)
                  .append(relation.getColumnName())
                  .append("(").append(relation.getColumnId()).append(")");
            
            // 显示作业信息
            if (relation.getRelJobName() != null) {
                graph.append(" [").append(relation.getRelJobName());
                if (relation.getSqlNo() != null) {
                    graph.append("/").append(relation.getSqlNo());
                }
                graph.append("]");
            }
            graph.append("\n");
            
            // 显示跳过的临时表信息
            if (relation.getSkippedTempTables() != null && !relation.getSkippedTempTables().isEmpty()) {
                String skipPrefix = getSkipPrefix(isLastTable, isLastColumn);
                graph.append(skipPrefix).append(SKIP_ICON).append(" 跳过临时表: ");
                graph.append(String.join(" → ", relation.getSkippedTempTables())).append("\n");
            }
            
        } else if (relations.size() > 1) {
            // 多分支关系
            graph.append(BRANCH_DOWN).append("━━━> 多个去向:\n");
            
            for (int relIndex = 0; relIndex < relations.size(); relIndex++) {
                RelationDTO relation = relations.get(relIndex);
                boolean isLastRelation = (relIndex == relations.size() - 1);
                
                String relationPrefix = getRelationPrefix(isLastTable, isLastColumn, isLastRelation);
                graph.append(relationPrefix)
                      .append(relation.getColumnName())
                      .append("(").append(relation.getColumnId()).append(")");
                
                // 显示作业信息
                if (relation.getRelJobName() != null) {
                    graph.append(" [").append(relation.getRelJobName());
                    if (relation.getSqlNo() != null) {
                        graph.append("/").append(relation.getSqlNo());
                    }
                    graph.append("]");
                }
                graph.append("\n");
                
                // 显示跳过的临时表信息
                if (relation.getSkippedTempTables() != null && !relation.getSkippedTempTables().isEmpty()) {
                    String skipPrefix = getSkipPrefixForMultiRelation(isLastTable, isLastColumn, isLastRelation);
                    graph.append(skipPrefix).append(SKIP_ICON).append(" 跳过临时表: ");
                    graph.append(String.join(" → ", relation.getSkippedTempTables())).append("\n");
                }
            }
        }
    }
    
    /**
     * 获取层级标题
     */
    private String getLevelTitle(Integer level) {
        String title;
        switch (level) {
            case 0:
                title = "Level " + level + ": 源数据层（ODS层）";
                break;
            case 1:
                title = "Level " + level + ": 数据仓库层（DWD层）";
                break;
            case 2:
                title = "Level " + level + ": 应用数据层（ADS层）";
                break;
            default:
                title = "Level " + level + ": 报表层（RPT层）";
                break;
        }
        return "┌─ " + title + " ─┐";
    }
    
    /**
     * 获取字段前缀
     */
    private String getColumnPrefix(boolean isLastTable, boolean isLastColumn) {
        if (isLastTable) {
            return isLastColumn ? TREE_SPACE + TREE_LAST : TREE_SPACE + TREE_BRANCH;
        } else {
            return isLastColumn ? TREE_VERTICAL + TREE_LAST : TREE_VERTICAL + TREE_BRANCH;
        }
    }
    
    /**
     * 获取关系前缀
     */
    private String getRelationPrefix(boolean isLastTable, boolean isLastColumn, boolean isLastRelation) {
        String base = isLastTable ? TREE_SPACE : TREE_VERTICAL;
        String columnBase = isLastColumn ? TREE_SPACE : TREE_VERTICAL;
        
        if (isLastRelation) {
            return base + columnBase + TREE_LAST;
        } else {
            return base + columnBase + TREE_BRANCH;
        }
    }
    
    /**
     * 获取跳过临时表信息的前缀（单一关系）
     */
    private String getSkipPrefix(boolean isLastTable, boolean isLastColumn) {
        if (isLastTable) {
            return isLastColumn ? TREE_SPACE + TREE_SPACE : TREE_SPACE + TREE_VERTICAL;
        } else {
            return isLastColumn ? TREE_VERTICAL + TREE_SPACE : TREE_VERTICAL + TREE_VERTICAL;
        }
    }
    
    /**
     * 获取跳过临时表信息的前缀（多分支关系）
     */
    private String getSkipPrefixForMultiRelation(boolean isLastTable, boolean isLastColumn, boolean isLastRelation) {
        String base = isLastTable ? TREE_SPACE : TREE_VERTICAL;
        String columnBase = isLastColumn ? TREE_SPACE : TREE_VERTICAL;
        String relationBase = isLastRelation ? TREE_SPACE : TREE_VERTICAL;
        
        return base + columnBase + relationBase + TREE_SPACE;
    }
    
    /**
     * 生成统计摘要
     */
    private String generateSummary(List<TableDTO> tables, boolean skipTempTable) {
        StringBuilder summary = new StringBuilder();
        
        // 计算统计数据
        int totalTables = tables.size();
        int totalColumns = tables.stream().mapToInt(t -> 
                CollectionUtils.isEmpty(t.getColumns()) ? 0 : t.getColumns().size()).sum();
        int totalRelations = tables.stream().mapToInt(t -> 
                CollectionUtils.isEmpty(t.getColumns()) ? 0 : 
                t.getColumns().stream().mapToInt(c -> 
                        CollectionUtils.isEmpty(c.getRelations()) ? 0 : c.getRelations().size()).sum()).sum();
        int maxLevel = tables.stream().mapToInt(TableDTO::getLevel).max().orElse(0) + 1;
        
        // 计算跳过的临时表数量
        int totalSkippedTempTables = 0;
        for (TableDTO table : tables) {
            if (!CollectionUtils.isEmpty(table.getColumns())) {
                for (ColumnDTO column : table.getColumns()) {
                    if (!CollectionUtils.isEmpty(column.getRelations())) {
                        for (RelationDTO relation : column.getRelations()) {
                            if (relation.getSkippedTempTables() != null) {
                                totalSkippedTempTables += relation.getSkippedTempTables().size();
                            }
                        }
                    }
                }
            }
        }
        
        // 构建摘要
        summary.append("╔").append(StrUtil.repeat("═", 60)).append("╗\n");
        summary.append("║").append(centerText("数据血缘关系统计摘要", 60)).append("║\n");
        summary.append("╠").append(StrUtil.repeat("═", 60)).append("╣\n");
        summary.append("║ 涉及数据表总数: ").append(String.format("%-45s", totalTables + "个")).append("║\n");
        summary.append("║ 涉及字段总数: ").append(String.format("%-47s", totalColumns + "个")).append("║\n");
        summary.append("║ 血缘关系总数: ").append(String.format("%-47s", totalRelations + "条")).append("║\n");
        summary.append("║ 数据处理层级: ").append(String.format("%-47s", maxLevel + "层")).append("║\n");
        
        if (skipTempTable) {
            summary.append("║ 临时表跳过: ").append(String.format("%-49s", "已启用")).append("║\n");
            if (totalSkippedTempTables > 0) {
                summary.append("║ 跳过临时表总数: ").append(String.format("%-43s", totalSkippedTempTables + "个")).append("║\n");
            }
        }
        
        summary.append("║").append(StrUtil.repeat(" ", 60)).append("║\n");
        summary.append("║ 说明: 数据从Level 0开始，经过多层处理，最终到达报表层 ").append("║\n");
        if (skipTempTable && totalSkippedTempTables > 0) {
            summary.append("║ 提示: 该关系跳过了中间的临时表，直接连接最终目标表 ").append("║\n");
        }
        summary.append("╚").append(StrUtil.repeat("═", 60)).append("╝");
        
        return summary.toString();
    }
    
    /**
     * 文本居中
     */
    private String centerText(String text, int width) {
        if (text.length() >= width) {
            return text.substring(0, width);
        }
        int padding = (width - text.length()) / 2;
        return StrUtil.repeat(" ", padding) + text + StrUtil.repeat(" ", width - text.length() - padding);
    }
    
    /**
     * 关系图构建器内部类
     */
    private static class RelationGraphBuilder {
        private final Map<String, Set<String>> relationMap;
        
        public RelationGraphBuilder(List<TableDTO> tables) {
            this.relationMap = new HashMap<>();
            buildRelationMap(tables);
        }
        
        private void buildRelationMap(List<TableDTO> tables) {
            // 构建字段间的关系映射，用于后续查找
            for (TableDTO table : tables) {
                if (!CollectionUtils.isEmpty(table.getColumns())) {
                    for (ColumnDTO column : table.getColumns()) {
                        String sourceKey = table.getDbName() + "." + table.getTableName() + "." + column.getColumnName();
                        
                        if (!CollectionUtils.isEmpty(column.getRelations())) {
                            Set<String> targets = column.getRelations().stream()
                                    .map(rel -> rel.getColumnName() + "(" + rel.getColumnId() + ")")
                                    .collect(Collectors.toSet());
                            relationMap.put(sourceKey, targets);
                        }
                    }
                }
            }
        }
    }
} 